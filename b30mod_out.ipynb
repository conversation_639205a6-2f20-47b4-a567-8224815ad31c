{"cells": [{"cell_type": "markdown", "id": "notebook-title", "metadata": {}, "source": ["# Neural Unit Modulation Analysis - B30 Dataset\n", "\n", "This notebook analyzes neural unit responses to balloon stimulation events in the B30 dataset.\n", "The analysis includes:\n", "1. Unit data extraction and preprocessing\n", "2. Balloon event integration\n", "3. Modulation index calculation\n", "4. Statistical significance testing\n", "5. Unit classification and visualization\n"]}, {"cell_type": "markdown", "id": "imports-section", "metadata": {}, "source": ["## 1. Imports and Configuration"]}, {"cell_type": "code", "execution_count": 1, "id": "imports-cell", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[32;1mnpyx[c4] version 4.1.3 imported.\u001b[0m\n"]}], "source": ["# Standard library imports\n", "from pathlib import Path\n", "from typing import List, Dict, Optional\n", "\n", "# Third-party imports\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from matplotlib.colors import TwoSlopeNorm\n", "from tabulate import tabulate\n", "\n", "# Local module imports\n", "from npx_utils import extract_unit_data\n", "from modulation_functions import (\n", "    modulation_index_single_trial,\n", "    compute_trialwise_mi,\n", "    compute_trialwise_mi_null,\n", "    modulation_flag,\n", "    compute_trialwise_modulation_flags\n", ")\n", "from plotting_functions import plot_units_grid_with_shading"]}, {"cell_type": "code", "execution_count": 2, "id": "configuration-cell", "metadata": {}, "outputs": [], "source": ["# Configuration constants\n", "MINIMUM_FIRING_RATE_HZ = 0.05\n", "BALLOON_OFFSET_DURATION_SEC = 200.0\n", "NULL_DISTRIBUTION_ITERATIONS = 10000\n", "MODULATION_WINDOW_LENGTH_SAMPLES = 40\n", "RANDOM_SEED = 42\n", "\n", "# Time limits (seconds) for filtering balloon events per animal\n", "# -1 indicates no time limit\n", "TIME_LIMITS_BY_ANIMAL = {\n", "    \"b24\": -1, \n", "    \"b25\": -1, \n", "    \"b27\": -1, \n", "    \"b28\": -1, \n", "    \"b30\": -1\n", "}\n", "\n", "# Dataset paths for Kilosort output folders\n", "DATASET_PATHS = [\n", "    # Uncomment additional datasets as needed\n", "    # Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b27\\\\b27_p1_r1_g0_p\\\\catgt\"),\n", "    # Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b28\\\\b28_p2_r1_g0\\\\catgt\"),\n", "    Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b30\\\\b30_p1_r1_g0\\\\catgt\")\n", "]\n", "\n", "# Mapping from recording directory names to animal IDs\n", "RECORDING_TO_ANIMAL_MAPPING = {\n", "    \"b24_p1_r1_g0_p\": \"b24\",\n", "    \"b25_p1_r2_g0_p\": \"b25\",\n", "    \"b27_p1_r1_g0_p\": \"b27\",\n", "    \"b28_p2_r1_g0\": \"b28\",\n", "    \"b30_p1_r1_g0\": \"b30\"\n", "}"]}, {"cell_type": "markdown", "id": "unit-extraction-section", "metadata": {}, "source": ["## 2. Unit Data Extraction\n", "\n", "Extract neural unit data from Kilosort output, including spike times, waveforms, and quality metrics.\n", "Only units with firing rates above the minimum threshold are retained."]}, {"cell_type": "code", "execution_count": 3, "id": "unit-extraction-cell", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Step 1] Processing units: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b30\\b30_p1_r1_g0\\catgt (rid=b30_p1_r1_g0, animal=b30)\n", "\n", "\u001b[34;1m--- New spike-sorting detected.\u001b[0m\n", "[ 15/15] 100.00% | Unit 324 | ETC: 00:00\n", "✅ Done.\n", "  ✔ Saved: Z:\\users\\izouridis\\projects\\bal_npx\\data\\b30\\b30_p1_r1_g0\\catgt\\unit_summary.pkl\n"]}], "source": ["# Extract unit data from Kilosort datasets and filter by firing rate\n", "for dataset_path in DATASET_PATHS:\n", "    recording_id = dataset_path.parent.name\n", "    animal_id = RECORDING_TO_ANIMAL_MAPPING.get(recording_id, \"\")\n", "    \n", "    print(f\"[Unit Extraction] Processing: {dataset_path}\")\n", "    print(f\"  Recording ID: {recording_id}, Animal ID: {animal_id}\")\n", "    \n", "    try:\n", "        # Extract unit data with good and MUA quality using imported function\n", "        unit_dataframe = extract_unit_data(\n", "            datapath=str(dataset_path), \n", "            animal_id=animal_id, \n", "            quality=['good', 'mua']\n", "        )\n", "        \n", "        # Filter by minimum firing rate\n", "        units_before_filter = len(unit_dataframe)\n", "        unit_dataframe = unit_dataframe[\n", "            unit_dataframe['mean_firing_rate'] > MINIMUM_FIRING_RATE_HZ\n", "        ]\n", "        units_after_filter = len(unit_dataframe)\n", "        \n", "        # Save filtered data\n", "        output_file = dataset_path / 'unit_summary.pkl'\n", "        unit_dataframe.to_pickle(output_file)\n", "        \n", "        print(f\"  ✔ Filtered {units_before_filter} → {units_after_filter} units\")\n", "        print(f\"  ✔ Saved: {output_file}\")\n", "        \n", "    except Exception as error:\n", "        print(f\"  ❌ Failed to process {dataset_path}: {error}\")"]}, {"cell_type": "markdown", "id": "balloon-integration-section", "metadata": {}, "source": ["## 3. Balloon Event Integration\n", "\n", "Load balloon stimulation timing data and integrate it with the unit data.\n", "Balloon events are filtered by time limits if specified for each animal."]}, {"cell_type": "code", "execution_count": 4, "id": "balloon-integration-cell", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Step 3] b30_p1_r1_g0 (b30) ✔ Saved 5->5 events in unit_summary.pkl (tmax=-1.0)\n"]}], "source": ["# Load balloon event timing data and integrate with unit summaries\n", "for dataset_path in DATASET_PATHS:\n", "    recording_id = dataset_path.parent.name\n", "    animal_id = RECORDING_TO_ANIMAL_MAPPING.get(recording_id, \"\")\n", "    base_path = dataset_path.parent\n", "    \n", "    # Define file paths\n", "    balloon_on_file = base_path / 'bal_on_sec.txt'\n", "    balloon_off_file = base_path / 'bal_off_sec.txt'\n", "    unit_summary_file = dataset_path / 'unit_summary.pkl'\n", "    \n", "    status_prefix = f\"[Balloon Integration] {recording_id} ({animal_id})\"\n", "    \n", "    # Validation checks\n", "    if not animal_id:\n", "        print(f\"{status_prefix} ❌ Animal ID not found. Skipping.\")\n", "        continue\n", "        \n", "    if not balloon_on_file.exists() or not balloon_off_file.exists():\n", "        print(f\"{status_prefix} ⚠ Missing balloon timing files\")\n", "        continue\n", "        \n", "    if not unit_summary_file.exists():\n", "        print(f\"{status_prefix} ⚠ Missing unit summary file - run extraction first\")\n", "        continue\n", "    \n", "    try:\n", "        # Load balloon timing data\n", "        balloon_on_times = np.atleast_2d(np.loadtxt(balloon_on_file, delimiter='\\t'))\n", "        balloon_off_times = np.atleast_2d(np.loadtxt(balloon_off_file, delimiter='\\t'))\n", "        events_before_filter = len(balloon_on_times)\n", "        \n", "        # Apply time limits if specified\n", "        time_limit = float(TIME_LIMITS_BY_ANIMAL.get(animal_id, -1))\n", "        if time_limit > 0:\n", "            time_mask = (\n", "                (balloon_on_times[:, 0] < time_limit) & \n", "                (balloon_on_times[:, 1] < time_limit) &\n", "                (balloon_off_times[:, 0] < time_limit) & \n", "                (balloon_off_times[:, 1] < time_limit)\n", "            )\n", "            balloon_on_times = balloon_on_times[time_mask]\n", "            balloon_off_times = balloon_off_times[time_mask]\n", "        \n", "        events_after_filter = len(balloon_on_times)\n", "        \n", "        # Load unit data and add balloon timing columns\n", "        unit_dataframe = pd.read_pickle(unit_summary_file)\n", "        unit_dataframe['balloon_on_sec'] = [balloon_on_times.tolist()] * len(unit_dataframe)\n", "        unit_dataframe['balloon_off_sec'] = [balloon_off_times.tolist()] * len(unit_dataframe)\n", "        \n", "        # Save updated data\n", "        unit_dataframe.to_pickle(unit_summary_file)\n", "        \n", "        print(f\"{status_prefix} ✔ Integrated {events_before_filter}→{events_after_filter} events (time_limit={time_limit})\")\n", "        \n", "    except Exception as error:\n", "        print(f\"{status_prefix} ❌ Integration failed: {error}\")"]}, {"cell_type": "markdown", "id": "summary-section", "metadata": {}, "source": ["## 4. Dataset Summary\n", "\n", "Generate a summary table showing unit counts and balloon event statistics for each dataset."]}, {"cell_type": "code", "execution_count": 5, "id": "summary-generation-cell", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Unit Summary ===\n", "\n", "|    | recording_id   | animal_id   |   units |   good_units |   mua_units |   balloon_on |   balloon_off |\n", "|----|----------------|-------------|---------|--------------|-------------|--------------|---------------|\n", "|  0 | b30_p1_r1_g0   | b30         |      15 |           11 |           4 |            5 |             5 |\n"]}], "source": ["# Generate summary table of unit counts and balloon events for each dataset\n", "summary_data = []\n", "\n", "for dataset_path in DATASET_PATHS:\n", "    recording_id = dataset_path.parent.name\n", "    animal_id = RECORDING_TO_ANIMAL_MAPPING.get(recording_id, \"\")\n", "    unit_summary_file = dataset_path / \"unit_summary.pkl\"\n", "    \n", "    if not unit_summary_file.exists():\n", "        continue\n", "    \n", "    try:\n", "        unit_dataframe = pd.read_pickle(unit_summary_file)\n", "        \n", "        # Count units by quality\n", "        good_unit_count = (\n", "            int((unit_dataframe.get(\"quality\") == \"good\").sum()) \n", "            if \"quality\" in unit_dataframe else None\n", "        )\n", "        mua_unit_count = (\n", "            int((unit_dataframe.get(\"quality\") == \"mua\").sum()) \n", "            if \"quality\" in unit_dataframe else None\n", "        )\n", "        \n", "        # Count balloon events\n", "        balloon_on_count = (\n", "            len(unit_dataframe.get(\"balloon_on_sec\", [[]])[0]) \n", "            if \"balloon_on_sec\" in unit_dataframe else 0\n", "        )\n", "        balloon_off_count = (\n", "            len(unit_dataframe.get(\"balloon_off_sec\", [[]])[0]) \n", "            if \"balloon_off_sec\" in unit_dataframe else 0\n", "        )\n", "        \n", "        summary_data.append({\n", "            \"recording_id\": recording_id,\n", "            \"animal_id\": animal_id,\n", "            \"units\": len(unit_dataframe),\n", "            \"good_units\": good_unit_count,\n", "            \"mua_units\": mua_unit_count,\n", "            \"balloon_on\": balloon_on_count,\n", "            \"balloon_off\": balloon_off_count,\n", "        })\n", "        \n", "    except Exception as error:\n", "        print(f\"  ⚠ Summary generation failed for {recording_id}: {error}\")\n", "\n", "# Create and display summary DataFrame\n", "dataset_summary = pd.DataFrame(summary_data)\n", "\n", "if not dataset_summary.empty:\n", "    print(\"\\n=== Dataset Summary ===\\n\")\n", "    print(tabulate(dataset_summary, headers=\"keys\", tablefmt=\"github\", showindex=True))\n", "else:\n", "    print(\"No summary data available - check dataset paths and processing steps.\")"]}, {"cell_type": "markdown", "id": "data-loading-section", "metadata": {}, "source": ["## 5. Data Loading and Preprocessing\n", "\n", "Load the processed unit data and prepare it for modulation analysis.\n", "This includes creating unique unit identifiers and adjusting balloon timing windows."]}, {"cell_type": "code", "execution_count": 6, "id": "data-loading-cell", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 15 rows (units) from: ['b30']\n"]}], "source": ["# Define paths to unit summary files\n", "unit_summary_paths = {\n", "    # Uncomment additional datasets as needed\n", "    # 'b27': Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b27\\\\b27_p1_r1_g0_p\\\\catgt\\\\unit_summary.pkl\"),\n", "    # 'b28': Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b28\\\\b28_p2_r1_g0\\\\catgt\\\\unit_summary.pkl\"),\n", "    'b30': Path(r\"Z:\\\\users\\\\izouridis\\\\projects\\\\bal_npx\\\\data\\\\b30\\\\b30_p1_r1_g0\\\\catgt\\\\unit_summary.pkl\")\n", "}\n", "\n", "# Load data from existing files\n", "dataframes = []\n", "available_datasets = []\n", "\n", "for dataset_name, file_path in unit_summary_paths.items():\n", "    if not file_path.exists():\n", "        print(f\"⚠ Missing file: {file_path}\")\n", "        continue\n", "        \n", "    unit_dataframe = pd.read_pickle(file_path).copy()\n", "    dataframes.append(unit_dataframe)\n", "    available_datasets.append(dataset_name)\n", "\n", "# Combine all datasets\n", "if dataframes:\n", "    neural_data = pd.concat(dataframes, ignore_index=True)\n", "else:\n", "    neural_data = pd.DataFrame()\n", "\n", "print(f\"Loaded {len(neural_data)} units from datasets: {available_datasets}\")\n", "\n", "if not neural_data.empty:\n", "    # Create unique unit identifiers\n", "    neural_data['unit'] = (\n", "        neural_data['animal_id'] + '_' + neural_data['unit'].astype(str)\n", "    )\n", "    \n", "    # Remove unnecessary columns to reduce memory usage\n", "    columns_to_remove = ['spike_times_samples', 'peak_channel', 'depth']\n", "    existing_columns_to_remove = [\n", "        col for col in columns_to_remove if col in neural_data.columns\n", "    ]\n", "    neural_data.drop(columns=existing_columns_to_remove, inplace=True)\n", "    \n", "    # Adjust balloon OFF timing by subtracting offset duration\n", "    neural_data[\"balloon_off_sec\"] = neural_data[\"balloon_off_sec\"].apply(\n", "        lambda intervals: [\n", "            [max(end_time - BALLOON_OFFSET_DURATION_SEC, 0.0), max(end_time, 0.0)] \n", "            if np.isfinite(end_time) else [start_time, end_time] \n", "            for start_time, end_time in intervals\n", "        ]\n", "    )"]}, {"cell_type": "markdown", "id": "modulation-analysis-section", "metadata": {}, "source": ["## 6. Modulation Analysis\n", "\n", "Calculate modulation indices for each unit and trial, generate null distributions for statistical testing,\n", "and classify units based on their modulation patterns."]}, {"cell_type": "code", "execution_count": 7, "id": "modulation-calculation-cell", "metadata": {}, "outputs": [], "source": ["# Perform modulation analysis if data is available\n", "if not neural_data.empty:\n", "    print(\"Calculating modulation indices using imported functions...\")\n", "    \n", "    # Calculate observed modulation indices using imported function\n", "    neural_data['modulation_index'] = neural_data.apply(compute_trialwise_mi, axis=1)\n", "    \n", "    print(\"Generating null distributions for statistical testing...\")\n", "    \n", "    # Generate null distributions for statistical significance testing\n", "    neural_data['modulation_index_null'] = neural_data.apply(\n", "        lambda row: compute_trialwise_mi_null(\n", "            row,\n", "            window_len=MODULATION_WINDOW_LENGTH_SAMPLES,\n", "            n_iter=NULL_DISTRIBUTION_ITERATIONS,\n", "            mi_formula='difference_over_sum',\n", "            seed=RANDOM_SEED\n", "        ),\n", "        axis=1\n", "    )\n", "    \n", "    print(\"Determining modulation significance flags...\")\n", "    \n", "    # Determine statistical significance flags for each trial using imported function\n", "    # -1: significantly suppressed, 0: not modulated, 1: significantly enhanced\n", "    neural_data['modulated_trial'] = neural_data.apply(\n", "        lambda row: compute_trialwise_modulation_flags(\n", "            row, \n", "            lower_quantile=0.1, \n", "            upper_quantile=0.9\n", "        ),\n", "        axis=1\n", "    )\n", "    \n", "    print(\"Modulation analysis complete.\")\n", "else:\n", "    print(\"No data available for modulation analysis.\")"]}, {"cell_type": "code", "execution_count": 11, "id": "70a8b1a1", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAkcAAAGGCAYAAABxHyV7AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAABi/0lEQVR4nO3deVxU5f4H8M+w7yAiAgIO4oIrSolrCuqtRE3UEstS1CzRMr3lem8sVm6pyc2rVhiipbebW1ZK7hqGuGuCiikiKpiYirKJ8Pz+8DI/xgFktjPM+Hm/XvOKOefh+Z4D4+HTc855jkwIIUBEREREAAAzQ28AERERUX3CcERERERUBcMRERERURUMR0RERERVMBwRERERVcFwRERERFQFwxERERFRFQxHRERERFUwHBERERFVwXBERERENSotLUW7du2wb9++GtucOHECXbp0gZ2dHTp37oxjx44prV+/fj38/f1hZ2eHIUOGID8/X89brR2GIyIiIqpWSUkJXn31VaSnp9fYprCwEGFhYXjuuedw7NgxdO/eHQMGDEBhYSEA4PDhwxg3bhxiYmJw6NAh3L59G5GRkRLtgWYYjoiIiEhFRkYGunbtiosXL9ba7rvvvoOtrS0+/fRTtG7dGkuXLoWjoyO+//57AMCyZcswfPhwjBo1Ch06dMDatWuxbds2ZGVlSbEbGmE4IiIiIhX79+9HaGgoUlNTa2136NAh9OzZEzKZDAAgk8nQo0cPxfcdOnQIvXr1UrT38fGBr68vDh06pL+N15KFoTegPhJC4N69e3B0dFT8somIiJ7ElP5+REVF1aldbm4u2rZtq7SscePGOHPmjGK9l5eXyvqrV6/qZkP1gOGoGgUFBXBxcUFOTg6cnJwMvTlERGQkCgoK4OPjgzt37sDZ2VlvdUpKSvDgwQO1v08IoRLarK2tYW1trfG2FBUVqXy/tbU1SktL67S+PmI4qsa9e/cAPBr6IyIiUte9e/f0Fo5KSkrgZ2uLPA2+18HBAffv31daFhMTg9jYWI23x8bGRiXolJaWws7Ork7r6yOGo2o4OjoCAHKOHYOTg4Pe6xU7e+i9BgD879o4STzzjHS1GjWSrlZysjR1cnOlqQMAX34pXa1//Uu6Wi1bXpCumERaFBVJViu/SaAkdaT6NwUAkyZJUaUAgI/i74g+PHjwAHkAcmQyqHNuowCAz/37KmdFtBk1AoAmTZogL085quXl5cHT07NO6+sjhqNqVA45Ojk4wEmPH/BKlhKdurO1laQMAECCTKkgwa9IQaqfoY2NNHUAwEzC2zKk/B9FByk/hBJxkvCXVepoesclKUlxvZGTmRmc1KkjBFBeDicnJ51eMtK1a1fMnz9fccpOCIGDBw/iH//4h2J9SkqK4vb9nJwc5OTkoGvXrjrbBl3j3WpERETGyMxM/ZeO5OXlobi4GADw8ssv486dO5gyZQoyMjIwZcoUFBYWYvjw4QAeXdi9du1arFq1CqdPn8aoUaMwcOBA+Pn56Wx7dI3hiIiIyBgZMBx5enriu+++AwA4OTnhp59+wq+//opnnnkGhw4dwrZt22Bvbw8A6NatG7744gvExcWhe/fuaNCgARITE3W2LfrA02pERETGyMwMUPe0mobEY9/7+Pvg4GAcP368xu+PjIys97NiV8VwREREZIwkDEdPG4YjIiIiY8RwpDcMR0RERMaI4UhvGI6IiIiMEcOR3jAcERERGSOZTL070Coq9LctJoa38hMRERFVwZEjIiIiY6TjuYvo/zEcERERGSOGI71hOCIiIjJGDEd6w3BERERkjBiO9IbhiIiIyBgxHOkNwxEREZExYjjSG4YjIiIiY8RwpDcMR0RERMaI4UhvGI6IiIiMkbozZPPxIXXGcERERGSM1B05YjiqM4OOx508eRK//fZbtesiIyMRGRlZp35CQkIQGxureB8YGAiZTKb0OnPmjA62mIiIiEydQUeOhgwZgpiYGHTv3l1lXXx8vEZ9lpeXIzMzE/v370fLli0Vy93c3DTeTiIionqHI0d6Y9BwJGr5RTk7O2vUZ1ZWFh48eIDg4GDY2NhoumlERET1G8OR3hjstFpISAiys7MxZswYyOVyyOVyREVFwdnZGQsWLFA6rSaEwNy5c+Hn5wcrKyt4eXkhLi6u2n4zMjLg4+PDYERERKatMhyp86I6MdhPatOmTfD29sbSpUsRHx+P7OxslJSU4NixY3j11VeV2q5ZswZLly5FQkICMjMzER0djdjYWBw/flyl37Nnz8LKygoDBw6Eh4cHevfujcOHD0u1W0RERNJgONIbg/2kXF1dYW5uDmdnZ8UptBkzZqB58+bw9fVVauvr64vExET07dsXcrkcEyZMgIeHB9LT01X6PXfuHG7fvo0333wT27ZtQ5s2bdC3b1/k5OTUuC2lpaUoKChQehEREdVrDEd6U69+UnK5vNrloaGhcHNzw6xZsxAeHo6mTZsiLy8P5eXlKm2/+uorXLx4EeHh4QgKCsLy5cvh5+eHtWvX1lh33rx5ipDm7OwMHx8fXe0SERGRfjAc6U29+knVdJ1QQkIC+vXrh5KSEgwbNgy7d++Gt7d3tW0tLCzg5OSkeC+TyRAQEIBr167VWHfWrFm4e/eu4lXbKBMREVG9oOdwVFJSgnHjxsHFxQWenp5YvHhxte1CQkJUps+RyWQYO3YsAOD27dsq6+r7HeQGvVtNJpPVqd3KlSsRHR2NadOmAQDu3LmDGzduVHu3W2hoKEJCQhATEwMAqKiowOnTpzFp0qQa+7e2toa1tbUGe0BERGQg6s6QXVGhVvfTpk3D0aNHsWfPHmRnZ2P06NFo2rQpXn75ZaV2mzZtwoMHDxTv09LSMHz4cEycOBHAoxulGjZsqDTfoFk9H8UyaDiyt7fHuXPnlEZ6qtOwYUPs2rULgwcPxr179zB79myUlZWhtLRUpe2gQYMwZ84cdOrUCa1atUJ8fDzu3LlT5wkliYiIjIK6o0FqtC0sLERCQgK2b9+OoKAgBAUFIT09HcuWLVMJR66uroqvy8vLMXv2bEyfPh3PPvssgEc3SrVs2RIeHh5131YDM2g4mjhxIqZPnw4rK6ta28XHx2PMmDEIDAyEu7s7IiIiYG9vjxMnTqi0nTp1KkpKSvDuu+/ixo0b6NKlC3bt2gVHR0d97QYREZH09BiOTp06hbKyMqVJmnv27IlPPvkEFRUVNY78rF69Gn/99RdmzJihWJaRkaE0KbMxMHg4qhx2e9zq1asVXwcEBCA1NbXGfvbt26f4WiaTYfbs2Zg9e7auNpOIiOipkpubCzc3N6XBi8aNG6OkpAS3bt1Co0aNVL5HCIEFCxZgypQpcHBwUCw/e/YsysrKEBwcjGvXruG5557DZ599Bk9PT0n2RRP1+6QfERERVU/DC7Ifn7qmuktUioqKVK7FrXxfXXvg0UDF1atXMX78eKXl586dQ0FBAT777DN89913uH79OgYOHFjtHef1hUFHjoiIiEhDGp5We3y6mpiYGKWHtwOP7h5/PARVvrezs6u2+w0bNqB///5K1yABQHp6OmQyGWxtbRXtPD09kZaWVu2zVesDhiMiIiJjpGE4ysnJUboRqrq7tZs0aYL8/Hw8fPgQFhaPokJeXh5sbW3h4uJSbffJyckqIQtQDVPu7u5o2LBhrVPsGBpPqxERERkjDU+rOTk5Kb2qC0cdO3aEpaUlDh06pFiWkpKCzp07V3sxdn5+Pi5duoQePXooLS8oKECDBg2wd+9exbJr164hPz8fAQEBuvpJ6BzDERERkTHS4ySQdnZ2GD16NCZMmIAjR45gy5YtWLRoEd577z0Aj0aRiouLFe3PnDkDGxsb+Pn5KfXj5OSE5557DlOnTsWRI0dw/PhxjBgxAi+++CLat2+vm5+DHjAcERERGSM9z5C9ZMkSPPPMMwgNDcWkSZMQFxeHoUOHAgA8PT3x3XffKdreuHEDLi4u1U7unJSUhKCgIISFhSEkJARyuRzffvutdvuuZ7zmiIiIyBipO0N2HZ9KUcnOzg5JSUlISkpSWff4EyoiIiIQERFRbT8NGjTA119/rVZtQ2M4IiIiMkZ6nATyacdwREREZIwYjvSG4YiIiMgYMRzpDcMRERGRMWI40hv+pIiIiIiq4MgRERGRMeLIkd4wHBERERkjhiO9YTgiIiIyRgxHesNwREREZIwYjvSG4YiIiMgY6XmG7KcZwxEREZEx4siR3jAcERERGSOGI71hOCIiIjJGDEd6w3BERERkjBiO9IbhqBb5Fh4otXDSe50vFuu9BACgfXtp6gCAXC5dLSmvMXRxkaZOSIg0dQAgN1e6WoMHlktW643IVpLU+ec/z0tSBwA2XuokWa2Da6WpU1goTR0ACA7Wf43ycuDYMf3XAcBwpEf8SRERERFVwZEjIiIiY8SRI71hOCIiIjJGDEd6w3BERERkjBiO9IbhiIiIyBhxhmy9YTgiIiIyRhw50huGIyIiImPEcKQ3DEdERETGiOFIb/iTIiIiMkaV4UidlxpKSkowbtw4uLi4wNPTE4sX1zxj8eDBgyGTyZReP/30k2L90qVL0aRJEzg6OmLcuHEoKirSeLelwJEjIiIiY6TnkaNp06bh6NGj2LNnD7KzszF69Gg0bdoUL7/8skrbjIwMfPPNN+jbt69iWYMGDQAAGzduRGxsLL755hs0btwYkZGRmD59OpYtW6bW9kiJ4YiIiMgY6TEcFRYWIiEhAdu3b0dQUBCCgoKQnp6OZcuWqYSj0tJSZGVloXPnzvDw8FDpKz4+HlOmTMHAgQMBAF988QWef/55LFy4EHZ2dnXffgnxtBoREREpOXXqFMrKytC9e3fFsp49eyItLQ0VFRVKbc+fPw+ZTIZmzZqp9FNeXo4jR46gV69eimVdu3bFgwcPcOrUKf3tgJYYjoiIiIyRHq85ys3NhZubG6ysrBTLGjdujJKSEty6dUup7dmzZ+Hs7Iw33ngDnp6eCA4Oxvbt2wEAd+7cQUlJCby8vBTtLSws0LBhQ1y9elXLH4D+MBwREREZIw3DUUFBgdKrtLRUpeuioiJYW1srLat8/3j7c+fOoaioCC+88AKSk5MRFhaGQYMG4ejRo4oLr6vrq7q69QXDERERkTGqnCG7rq//zZDt4+MDZ2dnxWvevHkqXdvY2KiEl8r3j18n9OGHH+LatWuIjIxEYGAgYmNj0b9/f3z55ZewsbFR+t6qfdXX640AXpBNRERknDS8IDsnJwdOTk6KxY+P6gBAkyZNkJ+fj4cPH8LC4lFUyMvLg62tLVxcXB7r1kxxZ1ql1q1bIz09HQ0bNoSNjQ3y8vIQEBAAAHj48CFu3boFT0/Pum+7xDhyREREZIw0PK3m5OSk9KouHHXs2BGWlpY4dOiQYllKSgo6d+4Ms8cCWWRkJMaOHau07OTJkwgICICZmRk6d+6MlJQUxbrU1FRYWloiMDBQlz8NneLIERERkTHS4638dnZ2GD16NCZMmIDExERcu3YNixYtQmJiIoBHo0jOzs6wtbXFSy+9hBEjRiAkJATdu3fHunXrkJKSgi+//BIAMHHiRLz99tto164dmjRpgqioKIwfP56n1YiIiEjH9DwJ5JIlSxAVFYXQ0FA4OzsjLi4OQ4cOBQB4enoiMTERkZGRGDp0KJYvX46PP/4YV65cQdu2bZGcnAy5XA4AGDFiBC5fvoy3334bpaWlGDZsGBYuXKjWtkiN4YiIiMgY6Tkc2dnZISkpCUlJSSrrhBBK79988028+eabNfY1c+ZMzJw5U636hsRwREREZIz44Fm9YTgiIiIyRgxHemPQn9TJkyfx22+/VbsuMjISkZGRdeonJCQEsbGxKsvT0tJgbm6Oy5cva76RRERE9FQxaDgaMmQIMjMzq10XHx+P+Ph4jfsuKyvD+PHjVZ4BQ0REZBL0+PiQp51BT6s9fkFXVc7Ozlr1vXDhQqVJroiIiExK5QzZ6rSnOjFYjAwJCUF2djbGjBkDuVwOuVyOqKgoODs7Y8GCBUqn1YQQmDt3Lvz8/GBlZQUvLy/ExcXV2HdmZib+/e9/Y/HixRLtDRERkcQ4cqQ3BvtJbdq0Cd7e3li6dCni4+ORnZ2NkpISHDt2DK+++qpS2zVr1mDp0qVISEhAZmYmoqOjERsbi+PHj6v0K4TAW2+9hdjYWDRu3Fiq3SEiIpIWw5HeGOwn5erqCnNzc8WD7wBgxowZaN68OXx9fZXa+vr6IjExEX379oVcLseECRPg4eGB9PR0lX5XrVqluN6orkpLS1WeUkxERFSvMRzpTb36SVXOpvm40NBQuLm5YdasWQgPD0fTpk2Rl5eH8vJypXZ5eXn4xz/+gS+++AIyNc6tzps3T+kJxT4+PtrsBhERkf4xHOlNvfpJ2djYVLs8ISEB/fr1Q0lJCYYNG4bdu3fD29tbpd0vv/yC/Px8dO3aFQ4ODmjbti0AoG3btpg7d26NdWfNmoW7d+8qXjk5ObrZISIiIn1hONIbg96tVtfRnZUrVyI6OhrTpk0DANy5cwc3btxQudtt6NCh6NGjh+L9tWvXEBISgm3btqF9+/Y19m9tbV3tU4mJiIjqLU4CqTcGDUf29vY4d+7cE2+5b9iwIXbt2oXBgwfj3r17mD17NsrKylBaWqrUztHREY6Ojor3FhaPdq9p06ZwdXXV/Q4QEREZCsOR3hg0HE2cOBHTp0+HlZVVre3i4+MxZswYBAYGwt3dHREREbC3t8eJEyck2lIiIqJ6huGoVqWlpRqfFTJ4OJo4cWK161avXq34OiAgAKmpqTX2s2/fvmqXy+XyWieaJCIiMlqcBBLDhg3DypUr0ahRI6Xle/bswYQJE2p8CseTPF0xkoiIiEzGrVu30Lp1a6xbtw4AcPv2bURGRuKFF17ACy+8oHG/Bh05IiIiIg3xtBr27duHhIQEvPvuu0hMTMTp06fRsmVLHD16FIGBgRr3y3BERERkjBiOAAD9+/fHli1bsGPHDgghMHTo0FrvUK8L0/xJERERmTrOc4SPP/4YAQEBKC0txdmzZ7Fx40YsXboUHTt2xK+//qpxv6b3kyIiInoaMBxh6dKl+Pzzz7Fz5074+/vjpZdeQkZGBkJCQtC3b1+N++VpNSIiImPE02o4d+4c3NzclJbZ29vjX//6F0aNGqVxvwxHRERExojhCG5ubrh06RJWrFiBCxcuYMWKFdi+fTtatWql9MQMdZneT4qIiOhpwNNqOHDgADp06ICsrCwkJyejuLgY586dQ2hoKDZt2qRxv6b3kyIiInoaMBxh+vTpmD9/PjZs2ABLS0sAwMKFC7Fw4UJER0dr3K/p/aSIiIieBnoORyUlJRg3bhxcXFzg6emJxYsX19j2559/RseOHeHg4IAOHTpg69atSutdXFwgk8mUXvfv39dot6v6/fffERYWprL8pZdewsWLFzXul9ccERERGSM9Pz5k2rRpOHr0KPbs2YPs7GyMHj0aTZs2xcsvv6zU7vTp0xg6dCg+/fRThIWF4ZdffsHLL7+MI0eOIDAwENeuXcPdu3dx8eJF2NnZKb7P3t5ere2pjlwux5EjR9CsWTOl5T///DPkcrnG/TIcERERkZLCwkIkJCRg+/btCAoKQlBQENLT07Fs2TKVcLRu3Tr06dMHkydPBgA0b94cW7duxX//+18EBgbi7Nmz8PT0VAkwuvDxxx8jMjISR48excOHD7FmzRpkZWXhP//5D9auXatxvzytRkREZIz0eFrt1KlTKCsrQ/fu3RXLevbsibS0NFRUVCi1HT16NObPn6/Sx927dwEAGRkZaNmypYY7WbshQ4bgwIEDuHHjBtq1a4cffvgBpaWlOHDgAIYPH65xvxw5IiIiMkZ6vJU/NzcXbm5usLKyUixr3LgxSkpKcOvWLTRq1EixvHXr1krfm56ejt27d2PChAkAgLNnz6KoqAghISE4f/48OnXqhKVLl+osMAUGBmLNmjU66asSwxEREZEx0jAcFRQUKC22traGtbW10rKioiKVZZXvS0tLayyRn5+PYcOGoUePHhg8eDCARxM1/vXXX5g7dy6cnJywYMEC9O3bFxkZGXB0dKz79v/P2LFj69z266+/Vrt/gKfViIiIjJOGp9V8fHzg7OyseM2bN0+laxsbG5UQVPm+6kXVVd24cQN9+vRBRUUFNmzYALP/1UtOTsbJkyfRr18/BAcH49tvv0VJSQl+/PFHjXZbCKF4FRYWYvXq1UhPT4ednR1cXFyQlZWFtWvXKuprgiNHRERExkjDkaOcnBw4OTkpFj8+QgQATZo0QX5+Ph4+fAgLi0dRIS8vD7a2tnBxcVFpf+3aNfTp0wcAsG/fPqXTbo+PTNnY2MDPzw/Xrl2r+7ZXkZiYqPg6IiICMTExiImJUWrz6aefYvfu3Rr1D3DkiIiIyDhpOHLk5OSk9KouHHXs2BGWlpY4dOiQYllKSgo6d+6sMiJTWFiIF198EWZmZti/fz+8vLwU64QQ8Pf3x+rVq5XaX7hwAQEBAVr/CH766Se89tprKssHDx6MAwcOaNwvwxEREZEx0uPdanZ2dhg9ejQmTJiAI0eOYMuWLVi0aBHee+89AI9GkYqLiwEAc+fOxcWLF5GUlKRYl5eXh7t370Imk2HAgAGIiYnBvn37kJ6ejjfeeAPe3t7VTt6orlatWqlcVySEwLJly9ChQweN++VpNSIiImOk5wfPLlmyBFFRUQgNDYWzszPi4uIwdOhQAICnpycSExMRGRmJjRs3ori4GF26dFH6/tGjR2P16tVYuHAhLC0t8dprr+Hu3bvo06cPtm3bBnNzc7W2pzrx8fEYNGgQNm7ciMDAQAghcPz4cRQVFSE5OVnjfhmOiIiIjJGeZ8i2s7NDUlKSYkSoKiGE4utz587V2o+NjQ0WL15c6+NHNPXcc8/hjz/+wHfffYezZ88CePS8tREjRlR7bVRdMRwREREZIz2PHBkLNzc3TJo0Sad9MhzVIjUVqOGORZ2qEsD1assWaeoAwOCwMslqfbbMUrJamZnS1BnWVbO7ODQRFtZEslo4elSyUnZ2XZ7cSAe++KKVJHUAoLxcslKSHS9GjpSmDgD8b05CvSouBo4d038deiQ7Oxv//Oc/ceTIEZSVlSmNaAHApUuXNOqX4YiIiMgYceQIb7zxBvLz8zFp0iSl6Qm0xXBERERkjBiOcPjwYRw/fhxt2rTRab8MR0RERMaI4QgtWrTAzZs3dd4vwxEREZExYjjCzJkz8eabb+L9999H8+bNlR6UCwC9evXSqF+GIyIiImPEcIQ33ngDADBx4kSVdTKZDOUa3sWgdjjatWsXkpOTcfz4cfz5558wMzODh4cHgoKC0L9/f/Tu3VujDSEiIiI1MByhoqJCL/3WORwlJSXhk08+wf3799G3b1/87W9/Q8OGDVFRUYH8/HycPn0aI0eOhIODA2bMmIExY8boZYOJiIgIT204unLlCnx8fCCTyXDlypVa2/r6+mpUo07hKDQ0FHK5HN988w2Cg4Nrbbt//358+eWXSExM1Oqhb0RERFQLPc+QXV/J5XLk5eXB3d0dcrkcMplMaX6jyvd6P622cuVKtGpVt4nOevfujd69ez9xOnEiIiLSwlM6cpSVlYVGjRopvtaHOv2kqgajsWPH4t69e4r3EydORH5+vsr3BAQE6GDziIiIqFqV4Uidlwlo2rQpZP8bBWvatGmtL03V6SfVoEED9O3bF9OmTUNSUhJOnTqFhw8fAgC++eYbFBQUaLwBRERERPVJncLRxYsXMXv2bDRq1AhCCIwdOxbOzs5o27YtiouLkZCQgL179yqNKBEREZEePaUjR1Ko00/K1dUVffv2xfTp0wEAKSkpuHPnDr777jtYWlrixo0bmDNnjsZXhRMREZGaGI70pk4XZAcHB+OZZ55Bp06dIJPJUFpaCktLS7Rr1w7m5ub4xz/+gWbNmqk8DZeIiIj05Cm9ILs6eXl5KCsrU8kher2Vf/ny5Th27BiOHj0KIQQCAgLg6emJtm3b4sGDB9i1axf69+8PHx8fjTaCiIiI1MRwhB07duCtt95CTk6O0nJJbuV/9tln8eyzzwIAEhIS8Mcff6CwsBBnzpzBjh078N///hcff/wxHjx4gLy8PI02hIiIiNTAcIR3330XXbp0wY8//ggnJyed9av240N69+4NW1tbeHp6onnz5ggNDcWqVavg6emplyfjEhERUTUYjpCTk4Pk5GT4+fnptF+1w9HevXuV3m/btk3xdeWkTERERKRnT+kM2VX16tULKSkphg9HREREVA9w5Ai9evVCVFQUfvrpJ7Ro0QJWVlZK66OjozXql+GIiIjIGDEcYefOnejcuTP+/PNP/Pnnn0rrZDIZwxEREdFTheFI5VIfXanTT+rAgQN1fqnj5MmT+O2336pdFxkZicjIyDr1ExISgtjYWMX7JUuWwNfXF3Z2dnjhhRdw4cIFtbaLiIio3tPzJJAlJSUYN24cXFxc4OnpicWLF9fY9sSJE+jSpQvs7OzQuXNnHDt2TGn9+vXr4e/vDzs7OwwZMqTaZ7Jq6sSJExg5ciSCgoIQGBiI4cOHY//+/Vr1WaeRo5CQkDp1pu6cAkOGDEFMTAy6d++usi4+Pr7O/VT17bffYs6cOVi3bh1atGiB2NhYDBo0CGfPnlU8qI6IiIhqN23aNBw9ehR79uxBdnY2Ro8ejaZNm+Lll19WaldYWIiwsDCMHDkSq1evxsqVKzFgwABcvHgR9vb2OHz4MMaNG4eVK1eiY8eOmDx5MiIjI/HTTz9pvY2bN29GREQEhg0bhjFjxqC8vBypqan429/+hu+//x6DBw/WqN86haOKigqNOn+S2mbUdnZ21qjPu3fvYuHChQgLCwMAzJgxA4GBgbh58ybc3d016pOIiKje0eNptcLCQiQkJGD79u0ICgpCUFAQ0tPTsWzZMpVw9N1338HW1haffvopZDIZli5dim3btuH7779HZGQkli1bhuHDh2PUqFEAgLVr16Jp06bIysrS+i6zDz/8EAsWLMDUqVMVy6ZMmYLPPvsMMTExGocjjU5APnz4ENeuXcOVK1dw5coVZGdnIzMzE999912d+wgJCUF2djbGjBkDuVwOuVyOqKgoODs7Y8GCBUqn1YQQmDt3Lvz8/GBlZQUvLy/ExcVV2+/EiRPx1ltvAXgUlP7973+jbdu2nGaAiIhMix5Pq506dQplZWVKZ3Z69uyJtLQ0lQGTQ4cOoWfPnoqzMzKZDD169EBqaqpifa9evRTtfXx84Ovri0OHDmmz9wCAS5cuYdCgQSrLBw0ahPPnz2vcr9rh6IcffoCXlxd8fX3h5+cHPz8/NGvWDK1bt8b7779f5342bdoEb29vLF26FPHx8cjOzkZJSQmOHTuGV199VantmjVrsHTpUiQkJCAzMxPR0dGIjY3F8ePHa+z/66+/houLC5KSkvDvf/+bp9SIiMi06DEc5ebmws3NTenW+MaNG6OkpAS3bt1Saevl5aW0rHHjxrh69Wqd1mujdevW2L59u8rybdu2QS6Xa9yv2nerzZw5E0OGDMHf//539OjRAz///DNu3bqFd999Fx9++GGd+3F1dYW5uTmcnZ0Vp9BmzJiB5s2bq7T19fVFYmIi+vbtCwCYMGEC4uLikJ6ejqCgoGr779evH44fP46vv/4agwcPxokTJ2ocvistLUVpaanifUFBQZ33g4iIyCA0PK32+N84a2trWFtbKy0rKipSWVb5vurfy9raVrZ70nptxMXFYdiwYUhLS0OXLl0APBqp2rBhA9auXatxv2qPHF26dAnTp09Hq1at8MwzzyAvLw9hYWFYvnx5rVey10VNKS80NBRubm6YNWsWwsPD0bRpU+Tl5dV68bevry86deqEf/3rX/Dx8UFSUlKNbefNm6cIac7OznyALhER1XsCMrVfwKPTWlX/5s2bN0+lbxsbG5XwUvnezs6uTm0r2z1pvTYGDhyI7du3o7i4GCtWrEBiYiIqKirw66+/Yvjw4Rr3q3Y4cnFxQVFREQAgICAAJ0+eVHydlZWl8YYAj36A1UlISEC/fv1QUlKCYcOGYffu3fD29q627d69e5XOM8pkMrRu3brW2wZnzZqFu3fvKl6PP92XiIiovqmoUP8FPHoeWdW/ebNmzVLpu0mTJsjPz8fDhw8Vy/Ly8mBrawsXFxeVto8/dD4vLw+enp51Wq+tPn36YOPGjcjIyMDx48exfv16BAcHa9Wn2qfVBgwYgIkTJ+KLL75ASEgIpk2bhkGDBmHDhg0q5xSfpK7XAa1cuRLR0dGYNm0aAODOnTu4ceNGtXe7LViwAE2bNsUXX3wBACgvL8fJkyfx3nvv1dh/dUOKRERE9VnVwFPX9gDg5OT0xCfYd+zYEZaWloqLrQEgJSUFnTt3htljp/K6du2K+fPnQwgBmUwGIQQOHjyIf/zjH4r1KSkpipuscnJykJOTg65du9Z946sYO3Ys4uPj4ejoiLFjx9ba9uuvv9aohtojR/Hx8WjRogWOHj2K8PBwdO3aFZ07d8a///1vtU+r2dvb49y5c/jrr79qbdewYUPs2rULmZmZOHbsGCIiIlBWVlbt+cqJEydi9erVWLduHc6fP4+oqCgUFxdj9OjRam0bERFRfabpyFFd2NnZYfTo0ZgwYQKOHDmCLVu2YNGiRYqBhry8PBQXFwMAXn75Zdy5cwdTpkxBRkYGpkyZgsLCQsVpraioKKxduxarVq3C6dOnMWrUKAwcOFDj2/irDowIIWp8aUPtkSNHR0elJPbNN99gxYoVsLGxgaWlpVp9TZw4EdOnT1d5UNzj4uPjMWbMGAQGBsLd3R0RERGwt7fHiRMnVNq+9NJLWLFiBWJjY5GTk4Nu3bphx44dcHBwUGvbiIiI6jNNR47qasmSJYiKikJoaCicnZ0RFxeHoUOHAgA8PT2RmJiIyMhIODk54aeffsKECRPw5ZdfokOHDti2bRvs7e0BAN26dcMXX3yB6Oho/PXXX3j++efx1VdfqbcxVSQmJiq+HjNmDLp166aSP0pLS6u9i62uZKIO8WrNmjWIiIiAtbU11qxZU2vbykmejFlBQQGcnZ3xzTd3YWdX+9CjLpw5o/cSAIBLl6SpAwCJX5ZJVuuzZeqFcm1kZkpTZ8U/r0lTCMBPJ5pIVmtgozTJar39dRdJ6vzv+C8JNR5AoLUtW6SpM3KkNHUAoEUL/dcoLi7ApEnOuHv37hNPXWmq8m/U9evq1SgoKICXl363TWrm5ubIy8tTmcvwxIkT6N69u2J0S111GjmKiYnBgAEDYG1tjZiYmBrbyWQykwhHREREVD+tWLECkyZNUlzf5OHhUW27fv36aVyjTuGo6l1oSUlJCA4OrvHOMiIiItI/fZ9Wq6+ioqLQtm1bVFRUoE+fPtiwYQNcXV0V62UyGezt7dG+fXuNa6h9zdHQoUOxd+9erYoSERGRdp7WcARA8TiSrKws+Pr66vwpGGqHo7Zt2+LQoUMMR0RERAb0NIejSo0aNUJ8fDzS09MVE0MLIVBaWooTJ07g7NmzGvWrdjhydXXFhAkTEBMTA7lcrnJ6bc+ePRptCBEREdWdEOoFHi3vbq+X3nzzTezevRt9+/bF999/j4iICFy4cAFHjhyp9RrpJ1E7HHXs2BEdO3bUuCARERFpjyNHwPbt2/H999+jX79+SE9Px9SpU/HMM8/g/fffR3p6usb91ikcNWvWDEeOHEHDhg21SmJERESkGwxHQElJCVq2bAng0WU/R48exTPPPIO3335bcV2SJuo0Q/bly5drfcgrERERSUufM2Qbi9atW2PXrl0AgHbt2iElJQUAcPfuXZSUlGjcr9qn1YiIiMjwOHIExMbG4pVXXkF5eTneeOMNtG3bFoMGDcLp06fx4osvatxvncPRokWL6vQIjujoaI03hoiIiOqG4ejRI8POnj2LiooK+Pj44Ndff8U333yDHj16YPLkyRr3W+dwdPDgwSc+A00mkzEcERERSYDh6JFmzZopvg4MDERgYKDWfdY5HG3evBnu7u5aFyQiIiLSlJ+fX50nfbyk4UNF6xSOdD3zJBEREWnnaR05io2N1XuNOoUjYYozRxERERmxpzUcjR49Wu816hSOHjx4AAsL9W5se/jwodrfQ0RERHXDGbKB0NDQWs9uafrUjjrNc9S1a1esWbMGZWVlT2xbUlKChIQEBAcHa7RBRERE9GSc5wgICQlB7969Fa8ePXrA09MTx48fR1hYmMb91mloZ9u2bZgxYwamTp2KF154AX/729/QunVruLm5oby8HLdu3cLp06eRkpKCbdu2YcCAAdi2bZvGG0VERES1e1pPq1VV01M7Vq9ejY0bN+KDDz7QqN86hSN3d3ckJibi8uXL+PLLL/H555/j9OnTqPjfT9rc3ByBgYF48cUXcfToUaXb6oiIiEj3GI5q1rt3b0ycOFHj71froiC5XI65c+di7ty5KC8vx19//QWZTIaGDRvyjjYiIiIJMRwBV65cUVl27949fPrpp5DL5Rr3q/EV0+bm5mjUqJHGhYmIiEhzDEePBm0eH5wRQsDHxwdff/21xv3ydjIiIiIjxHAEZGVlKb2XyWSwsrJC48aNtTqjxXBERERkhBiOgKZNm+qlX4ajWoSFAU5O+q+Tl6f/GgDwodcqaQoBQMlwyUpNbfSdZLXg8uTpLHTiz47S1AFQWtpEslpwcZGsVL9+0tR5peMFaQoBiN/WQrJa2ZmlktT5ZZ+1JHUA4IIEv6riYv3XeNqZmZnVeVSovLxcoxoMR0REREboaR052rt3r+LrI0eOYPHixYiOjkbnzp1hZWWF48ePIy4uDpMnT9a4Rp0mgSQiIqL6pXKG7Lq+dDlDthACM2fORKNGjeDq6orp06crpvepzqFDh9C9e3c4ODigVatWSEhIUFofGBgImUym9Dpz5ky1fVWd9DEhIQFr1qxBVFQUnn32WXTo0AGRkZFITEzEv/71L433jyNHRERERsiQI0dLlizBunXrsHnzZpSVleH111+Hu7t7tZMu5uXloX///oiKikJSUhKOHTuGMWPGwNPTEwMGDEB5eTkyMzOxf/9+tGzZUvF9bm5uT9yO69evw93dXWW5nZ0dbt++rfH+ceSIiIjICBny8SHx8fGYM2cOevbsidDQUCxYsADLli2rtu2WLVvg4eGBuXPnokWLFhgxYgRGjRqFdevWAXh0x9mDBw8QHBwMDw8Pxasuz2cdMGAAxo4di99++w2FhYW4f/8+9u7di7Fjx2L4cM2vfeXIERERkREy1MjR9evXkZOTg169eimW9ezZE9nZ2cjNzYWnp6dS+xdffBEdO3ZU6efu3bsAgIyMDPj4+MDGxkbtbfnyyy/x9ttvo3fv3orTehYWFhg1apRWp9U4ckRERGSENB05KigoUHqVlqp3Z2Jubi4AwMvLS7GscePGAICrV6+qtJfL5ejatavi/Z9//on//Oc/6Nu3LwDg7NmzsLKywsCBA+Hh4YHevXvj8OHDddoWR0dHrFu3Drdu3UJaWhrS0tKQn5+Pr776SqOwVYkjR0REREZI05EjHx8fpeUxMTGIjY1VWlZcXIxr165V28/9+/cBANbW/z8NQ+XXTwpaxcXFGDZsGDw8PPD2228DAM6dO4fbt2/jzTffxJw5c/DVV1+hb9++ihGlJ8nNzcWyZctw9uxZlJeXIyAgAG+++SZatNB86guGIyIiIiOkaTjKycmBU5VJ/KqGnEppaWkIDQ2ttp+FCxcCeBSEKkdnKkORnZ1djfXv37+PwYMHIzMzEykpKYq2X331FYqKihTbtHz5chw8eBBr167F7Nmza92nX3/9FWFhYWjfvj26deuG8vJy7N+/H59//jl27tyJHj161Pr9NWE4IiIiMkKahiMnJyelcFSdkJAQiBru/b9+/TqmT5+OvLw8xcNd8/43m/Hj1xtVKigoQP/+/fHHH39gz549SqM6FhYWStsjk8kQEBBQ48hVVe+//z7eeecdzJs3T2n5zJkzMW3aNPz2229P7KM6vOaIiIjICBnqbjUvLy/4+voiJSVFsSwlJQW+vr7VhqOKigoMHToUly5dwv79+9G2bVul9aGhoYiLi1Nqf/r0aQQEBDxxW86cOYOxY8eqLB83bhxOnjypxl4p48gRERGREaqcBFKd9roSFRWFGTNmwNvbG8CjkZr3339fsf7mzZuwtbWFg4MDVq1ahb1792Lr1q1wcXFRjDJZWVnB1dUVgwYNwpw5c9CpUye0atUK8fHxuHPnDiIjI5+4HXK5HIcPH1a5vigtLQ0eHh4a7x/DEREREall2rRp+PPPPzFkyBBYWFhg3LhxmDp1qmJ9586dERkZidjYWGzcuBEVFRUYOHCgUh+9e/fGvn37MHXqVJSUlODdd9/FjRs30KVLF+zatQuOjo5P3I7p06djwoQJOHfuHIKDgwE8mo37888/x9y5czXeP4YjIiIiI2TIGbLNzc2xZMkSLFmypNr1ly9fVnydnJxca18ymQyzZ89+4sXX1akcXfr888+xePFi2NjYoFWrVli1ahVeeeUVtfurxHBERERkhJ7WB88+LjIysk6n4NTBcERERGSEntZwNGfOnDq3jY6O1qgGwxEREZERelrDUWxsLMzMzNCpUyc4OjrWOOWATCZjOCIiInqaPK3haPny5diyZQtSU1PRq1cvhIeHY/DgwXBzc9NZDc5zREREZIQMNc+RoU2YMAHJycm4evUqRo4ciR07dqBly5bo3bs3li5diuzsbK1rMBwREREZoac1HFVydHTEiBEj8N133yEvLw8zZszA2bNn0b17dwQFBeGjjz7SuG+GIyIiIiP0tIejqqysrPDiiy9i5MiRGDFiBC5evIj58+dr3B/DERERkRFiOHr0MNsNGzZg1KhRaNy4MYYNG4Zbt24hMTERf/75p8b98oJsIiIiI2TIx4cY0tWrV7F161Zs3boV+/fvR5MmTfDSSy9hw4YN6NmzJ8zNzbWuYdBwdPLkSRQVFaF79+4q6yondFq9evUT+wkJCUFISAhiY2MBAImJiViwYAGuXr2Ktm3bYsmSJejRo4cOt5yIiIgMoWnTprC0tESvXr2waNEitG/fXrHu4MGDSm179eqlUQ2DhqMhQ4YgJiam2nAUHx+vUZ/JycmYNGkSvvrqK3Tp0gVJSUkICwvD2bNn4eXlpe0mExER1QtP6638Qgg8ePAAu3btwq5du2psJ5PJUF5erlENg15zVNPETQDg7OwMZ2dntftcvXo1Ro8ejZEjR6J58+b46KOP4OHhgZ9//lmbTSUiIqpXntZrjioqKur00jQYAQYMRyEhIcjOzsaYMWMgl8shl8sRFRUFZ2dnLFiwQOlZKUIIzJ07F35+frCysoKXlxfi4uKq7Xf69On4+9//rrL87t27+twdIiIiST2t4UgKBjuttmnTJgQGBuKDDz6AXC5HeHg4SkpKcOzYMVhZWSlN+b1mzRosXboU69evh7+/P5KTkxEVFYVBgwYhKChIqd/H3ycnJyMzMxN9+vSpcVtKS0tRWlqqeF9QUKCjvSQiItKPp/W0mhQMNnLk6uoKc3NzpdNnM2bMQPPmzeHr66vU1tfXF4mJiejbty/kcjkmTJgADw8PpKen11rj4sWLiIyMxMiRI1VCU1Xz5s1TbIezszN8fHy030EiIiI94siR/tSreY7kcnm1y0NDQ+Hm5oZZs2YhPDwcTZs2RV5eXq3nEzMzMxEaGgp/f3989dVXtdadNWsW7t69q3jl5ORosxtERER6x3CkP/UqHNnY2FS7PCEhAf369UNJSQmGDRuG3bt3w9vbu8Z+0tPT0atXL3h7e2P79u2wtbWtta61tTWcnJyUXkRERPUZw5H+GPRWfplMVqd2K1euRHR0NKZNmwYAuHPnDm7cuFHt3W65ubl4/vnn0aJFC2zfvh0ODg463WYiIqL6gNcc6Y9Bw5G9vT3OnTv3xJGahg0bYteuXRg8eDDu3buH2bNno6ysTOki6koffPABysvLsWrVKty/fx/3798HADg4ODAoERGRyXhaZ8iWgkHD0cSJEzF9+nRYWVnV2i4+Ph5jxoxBYGAg3N3dERERAXt7e5w4cUKpnRACmzdvRnFxMVq1aqW0LiYmRjGDNhERkbHjyJH+GDwcTZw4sdp1VR8bEhAQgNTU1Br72bdvn+LroqIiXW0eERERPYX44FkiIiIjxJEj/WE4IiIiMkIMR/pTr27lJyIiorox5K38QgjMnDkTjRo1gqurK6ZPn46KWgq89957kMlkSq9ly5Yp1lc+AcPOzg5DhgxBfn6+7jZWAwxHRERERsiQ4WjJkiVYt24dNm/ejI0bN+Lbb7/FkiVLamyfkZGBefPmITc3V/EaO3YsAODw4cMYN24cYmJicOjQIdy+fVvxbFVDYTgiIiIyQoYMR/Hx8ZgzZw569uyJ0NBQLFiwQGkk6HFnz55FUFAQPDw8FC87OzsAwLJlyzB8+HCMGjUKHTp0wNq1a7Ft2zZkZWXpboPVxHBERERkhAwVjq5fv46cnBz06tVLsaxnz57Izs5Gbm6uSvuCggJcu3YNLVu2rLa/Q4cOKfXl4+MDX19fHDp0SDcbrAGGIyIiIiOkaTgqKChQelU3oXJtKgOQl5eXYlnjxo0BAFevXlVpf/bsWchkMnzyySfw9vZGYGAgkpKSlPqr2ldlf9X1JRXerUZERGSENJ0h28fHR2l5dZMkFxcX49q1a9X2U/nkCWtra8Wyyq+rC1rnzp2DTCZDQEAA3n33Xezfvx9vvfUWnJycMGTIEBQVFSn1VdmfuqFNlxiOiIiIjJCmt/Ln5OQoPbbr8WACAGlpaQgNDa22n4ULFwJ4FIQqHxhfGWQqryOqatSoURg0aBBcXV0BAB06dEBmZiZWrFiBIUOGwMbGRiUIlZaWVtuXVHhajYiIyAhpelrNyclJ6VVdOAoJCYEQotrXyJEjAQB5eXmK9pVfe3p6qvQlk8kUwahS69atFSNTTZo0Ueqrsr/q+pIKwxERERHVmZeXF3x9fZGSkqJYlpKSAl9f32oDTXR0NPr166e07OTJkwgICAAAdO3aVamvnJwc5OTkoGvXrnragyfjaTUiIiIjZMgZsqOiojBjxgx4e3sDAGbOnIn3339fsf7mzZuwtbWFg4MDBg0ahHnz5mHRokUYMmQIduzYgTVr1mDv3r2KvkJCQtCtWzd07twZ7733HgYOHAg/Pz/dbbCaOHJERERkhAw5z9G0adMQERGBIUOG4JVXXsEbb7yBqVOnKtZ37twZixYtUny9YcMGrF27Fu3atcO//vUvrFu3Dt26dQMAdOvWDV988QXi4uLQvXt3NGjQAImJibrbWA1w5IiIiMgIGXLkyNzcHEuWLKlxVuzLly8rvR88eDAGDx5cY3+RkZEGnxW7KoYjIiIiI8QHz+oPwxEREZERYjjSH4YjIiIiI8RwpD8MR0REREZI0xmy6ckYjoiIiIwQR470h+GIiIjICDEc6Q/DERERkRFiONIfhqNapKUB9vb6r/Paa/qvAQDf7xgnTSEA/WWSlcK1zq9LVuv0aWnqhHhLUwcAfB9KV+uiRSvJav1vfjn9M3eQqBAQHCxZKcTMVX3elj6Eh0tSBgDQsqX+a9y7B0yfrv86AMORPnGGbCIiIqIqOHJERERkhDhypD8MR0REREaI4Uh/GI6IiIiMEMOR/jAcERERGSGGI/1hOCIiIjJCnCFbfxiOiIiIjBBHjvSH4YiIiMgIMRzpD8MRERGREWI40h+GIyIiIiPEcKQ/DEdERERGiOFIf/j4ECIiIqIqOHJERERkhDhypD8MR0REREaI4Uh/GI6IiIiMEMOR/vCaIyIiIiNUOUN2XV+6nCFbCIGZM2eiUaNGcHV1xfTp01FRQ/qKjIyETCZTefXp00fRxsXFRWX9/fv3dbfBauLIERERkREy5MjRkiVLsG7dOmzevBllZWV4/fXX4e7ujg8++EClbXx8PObPn694f/nyZYSEhGDy5MkAgGvXruHu3bu4ePEi7OzsFO3s7e11t8FqYjgiIiIyQoYMR/Hx8ZgzZw569uwJAFiwYAH++c9/VhuOnJ2d4ezsrHg/evRovPLKKwgPDwcAnD17Fp6enmjWrJnuNlBLDEdERERGyFDh6Pr168jJyUGvXr0Uy3r27Ins7Gzk5ubC09Ozxu/dvXs3Dhw4gMzMTMWyjIwMtGzZUjcbpyO85oiIiMgIqXO9UdUgVVBQoPQqLS1Vq25ubi4AwMvLS7GscePGAICrV6/W+r3z589HZGQkfHx8FMvOnj2LoqIihISEwNPTE2FhYUrhyRA4ckRERGSENB05qhpMACAmJgaxsbFKy4qLi3Ht2rVq+6m8UNra2lqxrPLr2oLWpUuXsGfPHsTHxystP3fuHP766y/MnTsXTk5OWLBgAfr27YuMjAw4OjrWad90jeGIiIjICGkajnJycuDk5KRYXjXkVEpLS0NoaGi1/SxcuBDAoyBkY2Oj+BqA0gXVj9u4cSM6duyINm3aKC1PTk5GWVkZHBwcAADffvstfHx88OOPP+K1116r497plkFPq508eRK//fZbtesiIyMRGRlZp35CQkJUUi/w6AccEhKi+QYSERGZGCcnJ6VXdeEoJCQEQohqXyNHjgQA5OXlKdpXfl3b9UbJycmKi7Crsra2VgQjALCxsYGfn1+NI1dSMGg4GjJkSI3nFePj41WG3tSxd+9evPXWWxp/PxERUX2m6TVH2vLy8oKvry9SUlIUy1JSUuDr61tjOBJC4MiRI+jRo4fKcn9/f6xevVqxrLCwEBcuXEBAQIBuNlgDBj2tJmqZkarqbX/qiouLw7x589CiRQuN+yAiIqrPDHkrf1RUFGbMmAFvb28AwMyZM/H+++8r1t+8eRO2traKEaHs7Gzcu3dP5ZSaTCbDgAEDEBMTA7lcjkaNGuHDDz+Et7c3wsLCdLfBajLYyFFISAiys7MxZswYyOVyyOVyREVFwdnZGQsWLFA6rSaEwNy5c+Hn5wcrKyt4eXkhLi6uxr537tyJX375BcOGDZNob4iIiKRlyBmyp02bhoiICAwZMgSvvPIK3njjDUydOlWxvnPnzli0aJHi/Y0bNwAADRo0UOlr4cKFePnll/Haa68hODgYZWVl2LZtG8zNzXW3wWoy2MjRpk2bEBgYiA8++AByuRzh4eEoKSnBsWPHYGVlhejoaEXbNWvWYOnSpVi/fj38/f2RnJyMqKgoDBo0CEFBQSp9Vw717d27t07bUlpaqnSFfUFBgZZ7R0REpF8VFYBMpl57XTE3N8eSJUuwZMmSatdfvnxZ6X2XLl1qPFtkY2ODxYsXY/HixbrbQC0ZbOTI1dUV5ubmSjNnzpgxA82bN4evr69SW19fXyQmJqJv376Qy+WYMGECPDw8kJ6erpNtmTdvnmI7nJ2dVW5zJCIiqm8Mdc3R06BeTQIpl8urXR4aGgo3NzfMmjUL4eHhaNq0KfLy8lBeXq6TurNmzcLdu3cVr5ycHJ30S0REpC8MR/pTr8JR5XwJj0tISEC/fv1QUlKCYcOGYffu3YqLwHTB2tpa5dZGIiKi+ozhSH8MerearI4nS1euXIno6GhMmzYNAHDnzh3cuHGj1rvdiIiITJkhrzkydQYdObK3t1dMG16bhg0bYteuXcjMzMSxY8cQERGBsrIytZ8HQ0REZCo4cqQ/Bh05mjhxIqZPnw4rK6ta28XHx2PMmDEIDAyEu7s7IiIiYG9vjxMnTki0pURERPULR470x+DhaOLEidWuqzpbZkBAAFJTU2vsZ9++fdUur+6RIkRERES14YNniYiIjBBHjvSH4YiIiMgIVc6QrU57qhuGIyIiIiOk7kgQR47qjuGIiIjICDEc6Q/DERERkRFiONIfhiMiIiIjxHCkPwxHRERERojhSH8YjoiIiIwQw5H+MBwREREZIYYj/WE4IiIiMkIMR/rDcERERGSEOAmk/pgZegOIiIiI6hOOHBERERkhdZ+txpGjumM4IiIiMkIMR/rDcERERGSEGI70h+GIiIjICDEc6Q8vyCYiIjJCFRXqv3RNCIHnn38eq1evrrVdVlYW+vXrB3t7e7Rp0wY7duxQWr9r1y60a9cOdnZ26NOnDy5duqT7jVUDwxEREZERMnQ4qqiowOTJk7Fz585a2wkhEB4eDg8PDxw9ehRvvPEGhgwZgitXrgAArly5gvDwcIwZMwZHjhxBo0aNEB4eDmHAoS6eViMiIjJChjytdu3aNbz++uu4dOkSXFxcam27d+9eXLx4Eb/99hvs7e3RunVr7N69G19//TViY2ORkJCAZ599Fu+//z4AIDExER4eHti/fz9CQkJ0t9Fq4MgRERGRETLkyNHx48fh4+ODY8eOwdnZuda2hw4dQlBQEOzt7RXLevbsidTUVMX6Xr16KdbZ2dkhKChIsd4QOHJUjcqhvMLCAknqFUhTBkVF0tQBpNsnALh/X7paUv0M792Tpg4g7c/P0lK6Wg8eSFOnwFy6X1Zhof2TG+lIaak0daT8/Jmb67/G/fuPDn5SnBISokDN0aBH21bw2AHa2toa1tbWatUeNGgQBg0aVKe2ubm58PLyUlrWuHFjXL16tU7rDYHhqBr3/veX6eWXfQy8JUREpm3BAkNvgX7cu3fviSMqmrKysoKHhwfy8tT/G+Xg4AAfH+Xvi4mJQWxsrNKy4uJiXLt2rdo+PD09lUaBnqSoqEglfFlbW6P0fwn8SesNgeGoGl5eXsjJyYGjoyNkdTyhW1BQAB8fH+Tk5MDJyUmv2ydVLVPcJylrmeI+SVnLFPdJylqmuE9S1tK0jhAC9+7dUxkJ0SUbGxtkZWXhgQbDo0IIlb9r1Y0apaWlITQ0tNo+Nm/ejPDw8DrXtLGxwa1bt5SWlZaWws7OTrH+8SBUWlr6xGuZ9InhqBpmZmbw9vbW6HudnJz0fnCQupYp7pOUtUxxn6SsZYr7JGUtU9wnKWtpUkdfI0ZV2djYwMbGRm/9h4SE6OzUYJMmTZCenq60LC8vD56enor1eXl5Kus7duyok/qa4AXZREREpDddu3bF8ePHUVxcrFiWkpKCrl27KtanpKQo1hUVFeHEiROK9YbAcEREREQ6dfPmTdz/39X2vXv3ho+PD8aMGYP09HTMnz8fhw8fxrhx4wAAY8eOxcGDBzF//nykp6djzJgx8PPzM9ht/ADDkc5YW1sjJiZG7Sv+63MtU9wnKWuZ4j5JWcsU90nKWqa4T1LWknKfTFHnzp2xaNEiAIC5uTl++OEH5Obm4plnnsE333yDzZs3w9fXFwAgl8uxadMmJCYmonPnzrh16xa2bNlS52t+9UEmDDkFJREREVE9w5EjIiIioioYjoiIiIiqYDgiIiIiqoLhiIiIiKgKhiMiIiKiKjhDdj2Xn58PNzc3AEB2djaSkpKQn5+P1q1bY/To0Yrp17XxzTffYPDgwXB0dNS6L00dOXIE2dnZ8PPzwzPPPKOzfvPy8vDzzz/j7t276NOnj8qMq4WFhVi8eDGio6O1rrVz506kpqYiPz8fDx48gJOTE+RyOUJCQtCmTRut+weAjz/+GJGRkRrP4K6Jv/76Cw0aNIBMJsODBw/www8/KH5XgwYNgpWVlc5r3r9/H5s2bVJ81vv376/T/i9cuFDt76p79+4GfWSBMZs4cSLmzJmjOF7pi76OFdWZP38+JkyYwM/E00hQvXThwgUREBAgzMzMRIcOHcSBAweEi4uLaN++vRg+fLho2bKl8PX1FefOndO6lkwmE35+fmLPnj062PLa3bx5U4SHhwtHR0fRu3dvce3aNfH8888LMzMz4e7uLszNzcVzzz0n8vPzta518OBB4ezsLFq1aiVatWolzMzMxKhRo0RJSYmiTV5enjAzM9OqTl5enujUqZNo2LCh6NKli/D29hZWVlYiPDxcBAcHC0tLSzF06FBx//59bXdJyGQy0aBBA5GYmKh1X09y6dIlERgYKMzMzIS/v7/4/fffRYcOHUSDBg1EcHCwaNSokfD39xdZWVla1blx44Z4/vnnhb29vRg4cKA4d+6c8PHxEW5ubiI4OFi4uLiI4OBgkZubq/U+3b9/X7z00kvCzMxMNG7cWFhaWgorKysRHBws3N3dhY2Njfj73/8uysvLta5VH4SFhYnr16/rrL/9+/fX+LKzsxPr169XvNeWVMeK7OzsGl8ODg7i119/VbynpwfDUT314osvipEjR4rTp0+Lt956S9ja2orJkycr1peXl4vx48eLvn37al1LJpOJGTNmCFtbWzFo0CBx5MgRrfusSUREhHjuuefEjz/+KF599VXh6+srunfvLnJycoQQjw6IL774onjttde0rtW1a1fx4YcfKt5v375deHh4iOeee07cu3dPCKGbcDR06FDxxhtviOLiYiGEEBUVFeKjjz4Sr776qhBCiOvXr4sePXqIcePGaVVHiEe/q2XLlgl3d3fRsWNH8f333+vtD/mLL74ohg0bJn7//XfxwQcfCBcXFzF48GBFyCspKRGjRo0SYWFhWtUZPny46Nevn9i6dat46aWXRMOGDUVERIQixBYUFIiXXnpJDB06VOt9Gjt2rHj++ecVQev+/fvirbfeEu+9954QQojDhw+LVq1aiWnTpmldKyQkRISGhtbppY2kpKQaX7a2tmLRokWK99pycHAQZmZmwszMTMhkshpf2v6bEkK6Y4W5ubnKPlV9X/W/9PTgJJBq8vf3r/PD+C5duqRxHXt7e5w+fRr+/v64e/cuGjRogBMnTiAwMFDRJjMzE506dUJhYaHGdYBHD9rNy8tDYWEhoqOjsX79enTu3Bmvv/46Bg8erNNTOK6urkhJSUGbNm1QXFwMJycnHDhwAN26dVO0OXPmDEJDQ3Hz5k2tajk6OuLUqVNo1qyZYtnFixfRp08f+Pr6YseOHSgoKICXlxfKy8s1ruPs7IwjR46gZcuWimUPHz6EnZ0d/vzzT7i4uCA9PR29evVSeTK1uip/VzY2Nvj000+xbNkyODo6IiIiAuHh4QgODoalpaVWNSo5Ozvj2LFjaN68OR4+fAhbW1ukpqbi2WefVbTJzMxEly5dcPv2ba3qpKWlISAgAH/++Sc8PDxw9OhRBAUFKdqkp6ejS5cuiscRaMrV1RUHDx5E69atFctKSkrQoEED3Lx5Ew4ODjh27BgGDBig8iBMdX377bd4++234e/vj6FDh9baNiYmRuM63t7eyM3NhYeHh8opzitXrsDLywsWFhaQyWRaHZMA4PLly4iKisL9+/fx5ZdfKv0cq/v3pg2pjhVpaWkYP348XFxcsHjxYri7uwN49PT6du3aYdu2bWjatCkAKP5Lpo/XHKkpKSkJr7zyCjw8PDBlyhS91XFzc8Mff/wBf39/ODs7Y9WqVWjYsKFSm2PHjuk0uPj5+WHt2rX45JNPsGrVKqxYsQKTJ0+Gj48P2rRpgwYNGuDbb7/Vqoa9vT3++usvAMDt27dRXl6OO3fuKLW5ffs2bG1ttaoDPPqjcfjwYaWDtb+/P3bu3InevXujf//+SEhI0LqOp6cndu/erRSOjh49CiGE4qnZN2/e1OkTtJ2cnPDRRx9h9uzZ+M9//oONGzdixYoVePjwIZo1a4YGDRrg4MGDWtVwc3PDhQsX0Lx5c1y8eBHl5eW4ePGiUji6cOECXF1dtapjZ2enCI3u7u6Ijo5WucbjypUrOrmWxcXFBb///rvSH/XLly/jwYMHiv/pEUJoFZYrjRw5El5eXggLC0Pfvn3Rs2dPrfusTkZGBqZPn449e/Zg+fLl6Nevn2Kdo6Mj9u/fr7PAIpfLsX37dnz77bd4/vnnERkZiQ8//FAv151Jdazo0qULjh8/jgULFmDIkCGIi4tTPPNLJpPB29uboehpZMBRK6N1+vRp4ejoKLZs2aK3GosXLxYNGjQQX331lcq68+fPi7ffflvY2Njo5NoTMzMzcePGjWrX5eTkiA0bNojY2FgRFRWlda2YmBjh6+srpk2bJtq0aSPat28vunbtKr7//ntx/vx5sXnzZuHv76+T0xpff/21sLOzE5MmTRJ//PGH0rozZ86IJk2aCE9PT62Hy9esWSMsLCxEZGSkWLFihfjnP/8pGjZsKN555x0hhBDz588Xrq6uYv78+VrVEeLRabWaflelpaXi6NGjYvXq1WLBggVa11qxYoVwcnISr7zyimjcuLHo1auX6NKli1i4cKH45ZdfxGeffSbc3Ny0rjVjxgzh5+dX7b+n69evi/nz54uGDRvqZJ8+/fRT4ejoKOLi4sT27dtFQkKC8Pf3FxEREUKIR6eo/P39xdSpU7WuVSk2NlY899xzOuuvJr/++qto3bq1eP3118XNmzeFEI9Og128eFEv9fLz88WoUaNE8+bNxY4dO4Sjo6NOa0l5rKh0/vx5ERISInr27CnS09N1vk9kPBiONLRy5UoxZMgQvdb45ptvxPLly1WW7927V7z44oti69atOqlT2x9cfVi5cqUYPHiwGDt2rLh8+bLYu3ev8PX1FTKZTNjY2IioqChRWlqqk1rbt28XI0aMECdOnFBZl5ubK1577TVhZ2endZ3k5GQxcOBA0aZNGxEaGipWrlypuBbos88+01mQlsvlOrlYva6Sk5PFe++9J+bMmSNu374tzpw5I5577jlhZ2cnmjdvLhYsWKD1NU/l5eXi448/FnPmzFFZ99NPP4mAgADx+eefi4qKCq3qVFq5cqVo166dsLOzE35+fmLmzJmisLBQCCHE9OnTxdKlS432guzS0lIRHR0tPDw8xJdffinJH/edO3cKf39/YWZmpvNaUh4rqvrqq69E48aNhYWFBcPRU4rXHFG9cfPmTbi6usLc3NzQm0Jk1DIyMjB+/Hikpqbijz/+0NlptZqUlJTg8OHD6NKliyRPsZfiWJGXl4cdO3Zg2LBhsLe311sdqp8Yjuqxq1ev4uuvv0ZqaiquXr2K0tJS2NnZwdPTE926dcPYsWN1ds3R9evXcejQIbRv3x4tWrRAZmYm4uPjFfOJTJw4Uek6DV3U6tChA5o3b65Uq1mzZpg4cSICAgJ0Uuvq1atYsWJFtXPahIaG6myuqNrq9OnTB6NGjdJJHalt374d69atw927d9GvXz+89dZbStdO3b59G8OGDcOePXu0qlPb50/Xn4naPn9+fn6YNGmS0X3+qlNWVoa8vDw0adIEZma6me/XwsICU6ZMwbx583R24X9Nli9fjrFjxyp93n744QesWLEC165dQ0BAAKZNm4bg4GCta0n1OSfjwRmy1WRhYYEPPvgAZWVleq2zc+dOBAQE4Ndff0XXrl0xadIkzJo1CxMmTECXLl1w4MABtG3bFnv37tW6VuXFxG+99Rbat2+PNWvWoHPnzrhw4QLatm2LK1euoFOnTjqv1a5dO5Va2dnZ6Nixo05qpaWloU2bNjhx4gSCg4PRrl07XL16FaGhofDz88OKFSsQEBCAzMxMvdZZvny5TupIbdWqVYr/a/by8kJ0dDSCgoKU7nh68OAB9u/fr1WdJ33+dPmZeNLn78qVK0b3+QOA//znP3jnnXewceNGCCEwZcoURQjz8vLCsmXLtK4BABUVFdi6dSvatm2LzZs366TPmrz77rsoKChQvF+zZg0iIiLQsmVLTJw4EQ0aNEBISAh++OEHrepI9TknI2PQk3pGSCaTiRYtWogWLVqITZs26a1O27Ztxbx582ptM2/ePNG+fXuta3Xq1EnMnTtXCCHE5s2bhZmZmdL8QEI8um7m2WefNapa3bp1E5999pnSsuTkZEXfFRUVIioqSvTr188o6gghhK2trWIOlie9tBUQECD+85//KN7fuHFD9OzZU3h4eIizZ88KIXQzTxQ/f9p9Lj799FPh7Owshg4dKtzc3MRLL70kfHx8xIYNG0RGRoZYs2aN8PDw0MkNAWZmZiInJ0fMnTtXuLi4iLZt24ovvvhC3LlzR+u+H/f4tZCBgYHi888/V2qzfPly0aZNG63qSPU5J+PCcKQmqQ4O9vb2T5z9Oj09Xdja2uqkVtVZji0sLFQuYL506ZJwdHQ0qloODg7i/PnzSssePnwoLCwsRF5enqKWg4ODUdQR4tHM6a1atRIdO3YU+/btq/WlLQcHB5W7/IqLi0WfPn2Eh4eHyMzM1MkfDX7+tPtcNG3aVGzfvl0IIURKSoowMzMTP//8s1Kbn3/+WTRp0kSrOkIoB5a//vpLfPzxx8LPz09YWVmJfv36iZiYGPHdd9+JHTt2aF3r8btovb29xe+//67U5uLFi1rfUCHV55yMC0+rqUkIASsrK8yaNQuXLl3Cq6++ivnz58Pd3R1/+9vfEBsbi//+97/YuXOnVnW6deuGuXPnoqSkpNr1paWl+Oijj9ClSxet6gBAQEAANm3aBADYvHkzKioq8PPPPyu12bp1K/z9/Y2qVvv27fHZZ58pTdqZmJgIW1tbxURvO3bsgI+Pj1HUAYDmzZtj586duH79Oi5cuIDevXvX+NJWhw4dsHr1aqVlNjY22Lp1K5o1a4aQkBAcP35c6zr8/Gn3ubh16xZatGgBAOjRowd8fHzg4eGh1MbPz0/ryWKBR/P+VGrQoAH+8Y9/4NKlS0hJSUHv3r1x7NgxzJgxA+Hh4VrXEkIgKSkJu3btwpUrV9C/f3/s2rVLqc2WLVsU+64pqT7nZGQMGs2MUE1zAh0+fFh89NFHYuDAgUIul2v9fzNZWVkiMDBQ8VyhV199VURGRopXX31VhIaGCmdnZ9GuXTud3GZa+dy2Ro0aCTMzM/HOO++IsLAwERYWJmbNmiUGDx4sLC0txY8//mhUtY4ePSpcXFxE8+bNRUREhOjZs6ewtLQUq1evFkIIMWLECOHg4CC2bdtmFHWq2rx5sxg/frzO+qtOamqqaNCggWjTpo1IS0tTWldQUCD69u2rePSCNvj50+5z8cILL4hRo0bV+Oy+69evKx4Foy0pp/2YPHmy+Nvf/ia8vb0Vj++wsLAQt2/fFkII0a9fP2FlZaX1lCZSfc7JuPBuNTVVPr6h8v/89G3Pnj1IS0tDbm4uioqKYGNjgyZNmqBr167o3bu3zu5CuXnzJg4ePIiGDRviueeew/3797Fw4UIcO3YMXl5eePPNN3UySmWIWklJScjKykLjxo0xbNgwtG3bFgCwb98+tGzZEl5eXkZTR2o3btzAli1b0L9/f/j6+iqtE0IgISEBmzZtwvbt27WqU91nYsGCBTh+/Lgknz991tL35+LixYsYMGAAOnXqhPXr1yut++GHHzB06FA8++yz+OGHH1RGlNSVlJSEESNGqHW7/tWrV+Hl5aXVserevXvIyMjA+fPnMWrUKACPHrkyaNAgpRnbNSXV55yMB8ORmgx1cCCqzcOHD1FQUKD1ozzqUy1T3Cd91RJC4MaNGyrh588//0RWVhY6d+6sdPyR8pjk5OSEkydP6n2uJV2T8jNB9Q//Wqtp9OjRak9y1qZNG1y+fFk/G6QDDx48wIwZM+Dj4wMnJycMHToUZ8+eVWpz48YNnUy4Zoq1pNwnQPW27ffeew+Ojo5o1KgRPDw8dHbbtpS1THGfqqs1ZcoUvdSSyWTVjgq5u7ujS5cuKiFIymOSpv//XVpaKtm/Kyk/E2QkDHEu72mjyfON9u/fX+eXtv7+97+LFi1aiPXr14t169aJ7t27Czs7O7F582ZFm7y8PCGTyVjLgHWEkPa2balqmeI+SV1LXfp85pquakn176o+/57IcBiOJKDJwaFdu3aK+WpkMlmNL11cJOjt7S1SUlIU7ysqKsT7778vLC0txX//+18hhO7m+TDFWlLuk5S3bUtVyxT3Sepa6jKGcCTVv6v6/Hsiw7Ew9MgVVe/o0aN49dVXkZWVhdTUVKWp7HWtqKgIDRs2VLyXyWRYtGgRzM3NMXLkSFhYWKB79+6sZeA6gLS3bUtVyxT3Sepapkiqf1f8PVG1DJ3Ongaa/p9TSUmJ6Nixo3j//ff1sFX/b9iwYWLAgAHi5s2bKuveeecdYWVlJaKjo3Uy8mGKtaTcJylv25aqlinuk9S11GUMI0dS/buqz78nMhyGIwlocyDKyMgQK1as0PEWKbt27Zro2rWrMDMzq3Zm29jYWGFhYaGTP+6mWEvKffrjjz9Eq1atxIgRI1TWbdmyRZiZmYng4GCRm5trNLVMcZ+krqUuKcORo6OjRrWk+ndVn39PZDgMRxLQ9OCgrpycHFFeXq7x9587d67Gx6BkZGQoXZDIWoarU1FRUe2B+saNG+LQoUMq/RpDLVPcJ6lrqUOqY5IQQri5uSk9skVdUvy7qq+/JzIcznMkgUaNGuHIkSOQy+V6rSPlfCKsZRx1TLWWKe6TlLW0PSYdOHAAqampuHr1KkpLS2FnZwdPT0/F5LSGYoqfPzIMXpCtIXUODjdv3pRkm6TMuaxlHHVMtZYp7pO2taQ4JmVlZSE8PByXL19GUFAQGjduDGtraxQUFODkyZOYO3cu/P39sXnzZjRt2lTjfdGUKX7+yDAYjtRU3w8ORPR0kfKYNH78eLRu3RqHDh2Cra2tyvqioiKMGTMGb731Fn755RetahEZEsORmnhwIKL6RMpjUmpqKo4ePVptHQCws7NDTEwMgoODtapDZGgMR2riwYGI6hMpj0nNmjXDL7/8gtatW9fY5qeffoK3t7fWtYgMieFITTw4EFF9IuUx6bPPPsOQIUOwdetW9OrVC15eXrC2tkZpaSny8vKQkpKCgwcPYtOmTVrXIjIkhiM11eeDg0wmYy0jqWWK+yRlLVPcJ01rSXlM6tevHzIyMpCQkIDU1FTk5uaiqKgINjY2aNKkCbp164avvvrKYNdbmuLnjwyD4UhN9fngYG1trfL0bdaqn7VMcZ+krGWK+6RpLamPST4+PoiLi9NJX7pmip8/MgzOc1TPSTmfCGsZRx1TrWWK+yR1LSlcuXIFhw8fRpcuXeDj44PNmzfj888/R35+Plq3bo3Zs2cjMDBQZ/VM8fNH9R/DkQakODjUdHtu5VD5iRMndHZ7LmtpV8sU90nKWqa4T1LXkiqwJCcnIzw8HPb29njw4AGio6MRHR2NN998E23atMHRo0exbt06bNiwAQMGDNCqlil+/siISD0lt7Hbvn27sLa2Fq6ursLBwUEsXLhQ2NjYiHfeeUcsX75cjB07VtjY2IiffvpJqzp9+/YVERERoqioqNr1hYWFYvjw4eL555/Xqg5raV/LFPdJylqmuE9S1pLqmCSEEB07dhRLliwRQgiRkJAgzMzMVJ79uGzZMtG2bVuta5ni54+MB8ORmqQ6ONjZ2YmMjIxa26Snpwt7e3ut6rCW9rVMcZ+krGWK+yRlLSkDi62treI5aWVlZcLCwkKcPHlSqc2FCxeM6ucn5WeCjAevKFPT+fPnMWTIEADA6NGjYWZmhm7duim1eeGFF3D58mWt6lTenlsbXd2ey1ra1TLFfZKylinuk5S1pDomAUDLli2xdetWAICFhQXOnTun8nyxVatWoX379lrXMsXPHxkP3q2mpsqDw+TJkxUHB3d3d6U2ujg4SHl7LmtpV8sU90nKWqa4T1LWkuqYBAALFizA0KFDcfnyZSxZsgT+/v6KdSkpKRg/fjzy8vJ08nQAU/z8kfHgBdlq+uWXXzB06FC8/fbbWLJkidK6xw8O2s5Im5OTg4SEBBw6dEjl9tyuXbti7NixOrtAkLWMo46p1jLFfZKqlpTHJAC4ePEirl69qnQH1927d3H9+nVs3boVr7/+Opo0aaJ1HcA0P39kHBiONCDlwYGI6EkMcUwqKyvDvHnzsHLlSty4cQMA4O3tjb///e947733dFqLSGq85kgD/v7+6N27N8rKyjBnzhx4eXnB1dUV7dq1w/Lly7Fhwwad1Lly5Qo2bNiAnJwcAMDmzZvRp08fdOjQARERETh16pRO6rCW8dQx1VqmuE9S1pLqmFTVu+++i1WrVmH+/Pk4deoUTpw4gX/+859YsGABZs+erZMapvj5IyNh2OvBjdvbb78tfH19RVJSkvj999/FqVOnxJdffik8PT3FrFmztOpbyttzWUu7Wqa4T1LWMsV9krpWJX0ekx7n5OQkDhw4oLJ8x44dwtXVVev+TfHzR8aD4UgL+jw4SHl7LmtpV8sU90nKWqa4T1LXqqTvwFKVt7e3SE1NVVmelpYmGjdurHX/pvj5I+PBcKQFfR4cpJxPhLW0q2WK+yRlLVPcJ6lrVdJ3YMnOzla8Pv30U9GyZUuxfft2kZ+fL27fvi0OHDggOnToIFauXKl1LVP8/JHx4DVHarpy5Yri9d5772H06NFITk7GrVu3cOfOHfz6668YP3681g9mlHI+EdbSrpYp7pOUtUxxn6SsJdUxCQDkcjn8/Pzg5+eH6dOn48KFCwgLC4O7uzsaNmyI3r174/fff8fEiRO1rmWKnz8yIoZOZ8ZGJpMJMzMzYWZmJmQymeL1+DIzMzOt6iQnJws7OzsxdepUlXW//vqrCAgIEC4uLiItLU2rOqylfS1T3Ccpa5niPklZS6pjkhBCXL58uc4vbZni54+MB2/lV1N2dnad22o7L4aUt+eylnHUMdVaprhPUtWS8pgkNVP8/JGRMHQ6oyd78OCBiIuLE56enor/G/T19RVLly5lrXpWyxT3ScpaprhPUtcyRab4+aP6jeHICEh5ey5rGUcdU61livskdS1TZIqfP6rfGI6MgJS357KWcdQx1VqmuE9S1zJFpvj5o/qNd6sZAScnJ1haWqosd3Z2rnY5axmulinuk5S1THGfpK5likzx80f1nKHTGVVPyvlEWEu7Wqa4T1LWMsV9krqWKTLFzx8ZD96tVk+ZmZlBJpMBAKr+ih5fJpPJUF5ezloGrGWK+yRlLVPcJ6lrmSJT/PyR8bAw9AZQ9bKysljLSGqZ4j5JWcsU90nqWqbIFD9/ZDw4ckRERERUBS/IJiIiIqqC4YiIiIioCoYjIiIioioYjoiIiIiqYDgiekrI5XKsXr1aZfnq1ashl8vr1Mfjbffs2YOzZ89W2zY2NhYhISE19hUSEoLY2Ng61SUikhLDERHVWUREBI4cOaJ437dvX9y4caPath988AE2bdok1aYREekM5zkiojqztbWFra1tndo6ODjoeWuIiPSDI0dEpHD58mXIZDJs2rQJ/v7+sLGxwcCBA/HXX38BUD6tVvnf0NDQak+PPX5abfPmzWjZsiXs7e3xzjvvcLZhIqq3GI6ISMXcuXOxfv167N+/H0eOHMHixYtV2lSeXtu4cSM++OCDWvvLyMjA8OHDERUVhWPHjqGsrAwpKSl62XYiIm3xtBoRqYiLi0NwcDAAYOTIkUrXGVVq1KgRAMDV1fWJp9ASExPRq1cvTJ06FQCwbNky/PTTTzreaiIi3eDIEdFTwtLSEhUVFSrLKyoqYGlpqbSsRYsWiq+dnJxQVlamVe2MjAx07NhRaVuqviciqk8YjoieEi4uLrh7967K8jt37sDFxUVpmZWVldJ7XTyC8fE+Hq9BRFRfMBwRPSU6dOiA1NRUleVpaWno1KmTXmu3a9dO6dRcRUUFTp06pdeaRESaYjgiekpERUVhy5Yt+OSTT/DHH3/g999/R1xcHH788UdMmjRJoz7t7e1x5syZakekqho/fjyOHj2KTz75BOfPn8cHH3yA7OxsjWoSEekbwxHRU+LZZ5/Fzz//jOTkZHTq1Ak9evTAnj17kJycjMDAQI36nDx5MqZNm/bEma6bN2+OrVu3Yv369ejYsSNyc3MRFhamUU0iIn2TCV1cTEBERERkIjhyRERERFQFwxERERFRFQxHRERERFUwHBERERFVwXBEREREVAXDEREREVEVDEdEREREVTAcEREREVXBcERERERUBcMRERERURUMR0RERERVMBwRERERVfF/MB/HfD//4GoAAAAASUVORK5CYII=", "text/plain": ["<Figure size 600x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.colors import TwoSlopeNorm\n", "\n", "def plot_mi_heatmap(\n", "    data,\n", "    *,\n", "    mi_col: str = \"modulation_index\",\n", "    unit_col: str = \"unit\",\n", "    vmax: float | None = None\n", "):\n", "    \"\"\"\n", "    Heatmap of modulation index (trials × units), diverging colormap:\n", "      blue < 0 < red, with 0 in white. NaNs shown in light gray.\n", "\n", "    X-axis: Unit id (unit names from `unit_col`)\n", "    Y-axis: trial1, trial2, ...\n", "    \"\"\"\n", "    # Collect MI lists per unit\n", "    mi_lists = data[mi_col].tolist()\n", "    n_units = len(mi_lists)\n", "    max_trials = max((len(m) for m in mi_lists), default=0)\n", "\n", "    # Build matrix (units x trials), then transpose to (trials x units)\n", "    M = np.full((n_units, max_trials), np.nan, dtype=float)\n", "    for i, mis in enumerate(mi_lists):\n", "        M[i, :len(mis)] = np.asarray(mis, dtype=float)\n", "    M = M.T  # now shape = (max_trials, n_units): rows=trials, cols=units\n", "\n", "    # Symmetric normalization around 0\n", "    if vmax is None:\n", "        finite = M[np.isfinite(M)]\n", "        abs_max = np.max(np.abs(finite)) if finite.size else 1.0\n", "    else:\n", "        abs_max = float(vmax)\n", "    norm = TwoSlopeNorm(vmin=-abs_max, vcenter=0.0, vmax=+abs_max)\n", "\n", "    # Diverging colormap with white center; gray for NaN\n", "    cmap = plt.get_cmap(\"bwr\").copy()\n", "    cmap.set_bad(\"0.85\", alpha=1.0)  # NaNs as light gray\n", "\n", "    # Figure size: wide for many units, tall for many trials\n", "    fig_w = max(6, 0.35 * n_units)\n", "    fig_h = max(4, 0.28 * max_trials)\n", "\n", "    fig, ax = plt.subplots(figsize=(fig_w, fig_h))\n", "    im = ax.imshow(M, aspect=\"auto\", interpolation=\"nearest\", cmap=cmap, norm=norm)\n", "\n", "    # Colorbar\n", "    cbar = fig.colorbar(im, ax=ax)\n", "    cbar.set_label(\"Modulation index\")\n", "\n", "    # Tick labels\n", "    # X: units\n", "    units = data[unit_col].astype(str).tolist() if unit_col in data.columns else [str(i) for i in range(n_units)]\n", "    ax.set_xticks(np.arange(n_units))\n", "    ax.set_xticklabels(units, rotation=90, ha=\"center\", va=\"top\")\n", "\n", "    # Y: trials as trial1, trial2, ...\n", "    ax.set_yticks(np.arange(max_trials))\n", "    ax.set_yticklabels([f\"trial{i+1}\" for i in range(max_trials)])\n", "    ax.invert_yaxis()  # put trial1 at the top\n", "\n", "    # Axis labels & title\n", "    ax.set_xlabel(\"Unit id\")\n", "    ax.set_ylabel(\"(Trial #)\")\n", "\n", "    fig.tight_layout()\n", "    plt.show()\n", "\n", "# usage:\n", "plot_mi_heatmap(data)\n"]}, {"cell_type": "code", "execution_count": 12, "id": "eda4d866", "metadata": {}, "outputs": [], "source": ["def classify_unit(modulated_trial):\n", "    n = len(modulated_trial)\n", "    n_pos = sum(x == 1 for x in modulated_trial)\n", "    n_neg = sum(x == -1 for x in modulated_trial)\n", "\n", "    if n_pos >= n / 2 and n_neg == 0:\n", "        return \"excited\"\n", "    elif n_neg >= n / 2 and n_pos == 0:\n", "        return \"inhibited\"\n", "    else:\n", "        return \"mixed/non-responsive\"\n", "\n", "data['unit_class'] = data['modulated_trial'].apply(classify_unit)\n"]}, {"cell_type": "code", "execution_count": 13, "id": "083ac1c7", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAewAAAGpCAYAAABPvwW/AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAABoPklEQVR4nO3dd1gU1xoG8Hd26b2JIEhRKUrs2IiKxhi7RmPsUdOLGm+M0XSNaTfNqLHExESNNfYuVrCjaAQUERSwI71Ih53v/kF2LiuoKGW2fL/n2UeZnZ35dln23TPnzBmBiAiMMcYY02oKuQtgjDHG2KNxYDPGGGM6gAObMcYY0wEc2IwxxpgO4MBmjDHGdAAHNmOMMaYDOLAZY4wxHcCBzRhjjOkADmzGmM7TxvmfnrQmbXwuTDtwYDNZ9OjRA4IgaNwUCgVsbGwQGBiI1atXy12izpo9ezYEQaj3/QqCgNmzZ0s/r1+/Hp6enjA1NcWbb76JiRMnwsvLq1b3mZ2djfHjx+PYsWPSsh49eqBHjx61up9H+emnnzBu3LgH1lQdt27dwoABA3D9+vXHepwcz1dt+PDhmDhxYqXly5Ytq/T3LQgCJk+eDADIyMiAh4cHkpKS6rli3WYkdwHMcLVt2xaLFy+WflapVLh58yZ+/vlnvPTSS3BwcED//v1lrJA9jlOnTsHd3V36efLkyfDx8cHKlSvh5uYGhUKBqVOn1uo+IyMjsWrVKrzyyivSsorvqfoQGxuLb775BhcuXHhgTdVx8OBB7Nmzpy5KrHWiKGLatGnYvHkzJkyYUOn+yMhI+Pn5YcWKFRrLXVxcAACOjo6YNm0aXn75ZYSGhsryBVMXcWAz2djY2KBz586Vlvfr1w/Ozs5YsWIFB7YOuf93mZGRgUmTJtV7669Fixb1ur+ZM2dizJgxaNSoUb3uVy7R0dGYMmUKIiIiYG5uXuU6kZGR6NChQ5V/32pvv/02vvrqK2zduhXDhg2rq3L1Ch8SZ1rH3NwcpqamGt+6RVHEf//7XzRr1gympqbw9fXFL7/8ovG4hIQEDB48GI6OjrCwsECXLl00WiyzZ8+Gl5cXdu3aBX9/f1hYWKBz584ICwvT2E5ycjJeeeUVNG7cGObm5ujYsSN27NihsY4gCFi8eDFee+01ODg4wNraGiNGjEBKSkq16wGAixcvYuDAgbCxsYGNjQ2GDh2KxMTER75Ge/bswdNPPw1LS0s0atQIb731FrKzs6tcV6VS4bvvvsNTTz0Fc3NzWFpaIigoCKGhodI6hYWFeOedd+Du7g5TU1P4+/vjxx9/1NjO/Pnz4e/vDzMzM7i5ueGdd95Bbm6uxmsye/ZshIWFSb+7OXPmQBAEXLt2rdIhcSLCzz//jObNm8Pc3BzNmjXDjz/+qNGHu2zZMgQGBsLS0hLm5uZo06YNNm7cCAAICwtDz549AQA9e/aUvhjcf4i4qKgIX375pVS7j48PvvvuO4iiKK3To0cPvPbaa/juu+/g4eEBMzMzPP300zhz5sxDfw8XL17Erl27MGbMmIfWBAB///03AgMDYWVlBRcXF7z11lvIysoCAKxYsQIvv/wyAMDb21s6zFxYWIiPPvoIPj4+MDU1hY2NDXr37o3IyMiH1lXRihUrYGRkhNOnT6NLly4wMzODp6enxu93xYoVVR7CVt8qdnWMHz8eKpUK4eHhcHZ2rrQ/IkJ0dDTatGnz0LpMTU3xwgsv4Ntvv632czF4xJgMgoODqXv37lRaWirdCgsL6fLlyzRmzBgCQDt37pTWf+ONN8jY2JhmzZpF+/bto48//pgUCgXNmTOHiIhUKhX5+/vTM888Q7t376b9+/fTgAEDSKlU0pUrV4iIaNasWWRhYUH29vY0f/582rVrF/Xs2ZNMTEzo/PnzRER09+5dcnNzo6ZNm9Jff/1Fu3fvphdffJEEQaDVq1dL9QAgW1tbmjhxIu3bt4+WLFlCZmZmNGrUqGrXExcXR9bW1tShQwfasmULbdiwgVq1akUuLi6UkpLywNdu586dJAgCPf/887Rr1y7666+/yNnZmZ577jnpeVb8054+fTpZWFjQggULKCwsjNasWUN+fn7k4OBA+fn50uvr5eVF69ato9DQUJoxYwYBoD///JOIiNauXUsmJibSNn799VeysrKi8ePHa7wms2bNopycHDp16hQBoFdffZVOnTpFRUVFNGHCBPL09NSoS6lU0owZM+jAgQP0zTffkEKhoG+++YaIiBYuXEgKhYK+/PJLCg0Npc2bN1PHjh3JyMiIbt68STk5ObRo0SICQIsWLaKYmBjpvRUcHExERKIo0rPPPktWVlb0ww8/0P79++mjjz4ipVJJr7/+usb70dbWljp37kzbtm2jLVu2UJMmTcjd3Z3Kysoe+Lv48MMPqVGjRiSKIhHRA2v68ssvSRAEmjRpEoWEhNDixYvJ0dGRWrVqRQUFBZSamkqffvopAaAtW7bQ1atXiYho+PDh5OzsTH/88QeFhYXR77//Tq6urtS8eXNpnxWfb1WWL19OgiCQh4cHzZs3jw4dOiT9jYWEhBARUWpqKp06deqBt5s3b0rbi46Olv7v6elJEyZM0NjflStXCACNHDmSfH19ycjIiHx9fWnlypWVajtw4AABoLi4uAfWz/6PA5vJIjg4mABUugmCQK1ataKNGzdK68bFxZEgCPTf//5XYxuffvopmZmZUXp6OiUnJxMAWrNmjXR/dnY2vffee3Tx4kUi+n+Q/fXXX9I6BQUF5OLiQiNHjiQiohkzZpCJiQldu3ZNY1+9evUiFxcXUqlURFQeTl27dtVY5+WXXyYrKysiomrVM2bMGGrYsCHl5ORI62RkZJCtrS1Nnz79ga9d+/btqW3bttIHNhHR+vXrydfXl+7evVspsMeMGUPz5s3T2MbmzZsJAJ06dYqIiPz8/DQCjIhozpw5tGvXLiIievPNN8nPz096/kREq1evpgULFkg/qwP7QT9XDOysrCwyMjKi//znPxr7fPfdd6lv375ERDRt2jSaOXOmxv3nzp0jALRu3ToiIgoNDSUAFBoaKq1TMcD27Nmjsb7al19+SQCk30VwcDBZWFho/C5WrlxJAOjs2bP0IB07dqQhQ4ZoLLu/pszMTDI1NaU333xTY72jR49KwU5UHqwAKCkpiYiIiouLqU+fPvT3339rPO6nn34iAJScnFzp+VZFvd1ly5ZJy4qKisjMzIwmT578wMdVR1WBvXHjRgJAHTt2pJ07d1JISIj0BeG3337TWDc7O5sA0OLFi2tUh6HgPmwmm3bt2mHp0qUAgDt37uCTTz5BaWkpNmzYAD8/P2m9w4cPg4gwaNAglJWVScsHDx6Mr776CseOHcOQIUPQokULvP7669i3bx/69OmDfv36Ye7cuRr7NDIywujRo6Wfzc3N0b9/f+lQdVhYGIKCguDp6anxuHHjxuHll1/G5cuXpT7SLl26aKzj7u6O/Px8AEDDhg0fWc+hQ4fQo0cPWFhYSM/LxsYG3bp1w4EDB6p8zQoLC/HPP//giy++0OgyGDlyJEaOHFnlY9asWQMASEtLQ1xcHK5cuYKdO3cCAIqLiwGUH7799ddfcevWLfTv3x8DBgzAZ599Jm2jZ8+eWLp0Kdq3b4+hQ4eif//+GDNmzBMPFgoPD0dZWVmlvsv58+dL///pp58AlI+6vnz5Mq5evSodxlfX/ShhYWEwMjLCiy++qLF83Lhx+Oyzz3DkyBEEBAQAAAICAmBjYyOtox5Ap/6dViUxMRFBQUEPrSE8PBzFxcUa7zsA6NatGzw9PREWFoZ33nmn0uNMTEwQEhICALh9+zbi4+MRHx+PXbt2Aaj+a6BW8f1qamqKBg0aSM+NiKBSqR74WIVCAYWiej2o3bt3x86dO9GzZ09YWloCAPr06YPU1FR8/vnneO2116T3ja2tLezs7Hi0eDVxHzaTjbW1NQIDAxEYGIjBgwfj4MGDyMrKQu/evZGeni6tl5GRAaD8A9XY2Fi6dezYEUB52AuCgAMHDmDChAnYt28fxo4di4YNG2LkyJFSPyFQPkrVyEjze6qzszMyMzMBAJmZmdJI1orUyyr2E1tYWGiso1AopP7X6tSTkZGBv//+W+M5GRsbY9euXbhz506Vr1lmZiaIqMq+wwc5e/YsOnbsCGdnZ/Tp0wdLliyRPnzV9c6bNw9fffUVkpKSMGXKFDRp0gRBQUGIiooCUP6FYO3atbCyssKcOXPQoUMHNGnSBBs2bKh2HRWpf6cPex4JCQl49tlnYW9vj+DgYPzwww8oLS3VqPtRMjMz4eTkBKVSqbG8ur9PABp93ffLycmRQulhNVTc5/11PGjsAQDs27cPzZs3h7u7O4YMGYLVq1fD1NQUwOOfr13V81M/t5UrV1Z6H1a8zZkzp9r7cXZ2xsCBAyu9LgMGDMDdu3c1xnkAgKWlJXJych7ruRgqDmymNRo2bIhFixbh5s2bePfdd6XldnZ2AMpb2hEREZVuw4cPBwA0atQIixcvRnJyMs6fP48ZM2Zg8+bN+PTTT6VtqYOiopSUFCk4HBwccPfu3UrrJCcnAwCcnJyq/XweVY+dnR1GjRpV5XPav39/ldu0tbWFIAhIS0vTWF5UVIS9e/dK4aCWm5uLvn37wsrKCjExMbh37x7OnDlT6ZQjU1NTfPLJJ4iNjcX169excOFCJCYmSoOpAGD06NE4duwYMjIysGHDBjg6OmLs2LEP/HLxMOrf6f3P48aNGwgNDUVxcTEGDBiA1NRUREREID8/H1FRUfjwww8faz8ODg5IT0+v1Hp8kt9nVZycnB4auOoaADzwffWgGhISEvD888+jTZs2uHr1KnJycnDs2DEMGjSoRjVXZdCgQVW+D9W3N954o9rbOnbsGFauXFlpeWFhIZRKpfR6qGVlZdX492AoOLCZVhk+fDj69u2LdevW4ciRIwDKD7EBQHp6utQiDwwMRFpaGj777DNkZGTg1KlTaNiwISIiIiAIAtq0aYOvvvoKLVu21JiIorCwEPv27dP4ec+ePejVqxcAIDg4GCdPnqw0ecXq1avh4uKCZs2aVet5VKee4OBgXLp0CW3atJGeU/v27TF37lxs3bq1yu1aWVmhTZs20iFttb1796J///6VwvPy5cvIyMjA1KlT0aJFC6nVuHfvXgDlrcfCwkL4+vpKh6A9PDwwadIkjB49Wqp15MiRGDp0KIDyLw0vvvgiPvvsM5SVlT1RYHfq1AnGxsaVnsdPP/2EUaNGIScnB3FxcXj11VcRGBgoHRWpWDeASi3n+wUHB6OsrEwaWa6mnpina9euj117RZ6enrh586bGsvtr6tSpE0xNTbFu3TqN5ceOHcONGzekGu5/3Llz51BUVIQPP/wQTZs2lQ4j3/8a1AZHR0eNv637b49zylpoaCgmTpyI+Ph4aZkoiti0aROCgoJgYmIiLc/KykJBQUGlLihWNe7DZlpn3rx5aNmyJd599138888/aNmyJcaNG4fXX38d165dQ2BgIOLi4vDxxx/D29sbvr6+KC0thYWFBV566SXMnj0bLi4uOHjwICIjIytN1jFx4kR8/fXXcHZ2xg8//ID8/Hyp1Ttt2jSsWrUKvXr1wuzZs+Ho6IiVK1fi8OHD+PPPP6vdj9e2bdtH1vP555+jS5cuGDhwIN5++22YmZlh6dKl2LZtGzZt2vTAbc+ZMweDBw/G6NGjMWHCBNy9excfffQRnn/+eTz11FMaj/Xz84ONjQ2+/vprGBkZwdjYGJs2bcIff/wBoLx/1tzcHO3bt8cXX3wBExMTtGrVCnFxcVixYoV09OKZZ57BW2+9henTp6N///7IysrC7Nmz4ePjg9atW1f/l/svJycn/Oc//8HcuXNhamqK4OBgnD59GosXL8aPP/4IZ2dneHl5YeHChXB3d4e9vT1CQkIwb948qW7g/y313bt3w97evlIt/fr1Q8+ePfH666/j9u3baN26NY4cOYL//ve/mDBhQo3P2X7uueewePFiEJEUqFXV9OGHH2LOnDkwNjbGoEGDkJSUhM8++wwtWrSQJh5RP27Lli3o378/2rVrByMjI8ycORPvv/8+iouLsXz5cuzevVvjNdA2b775JpYsWYJBgwZhzpw5sLS0xOLFi3Hx4kUcPXpUY93jx48DKO/jZtUg33g3ZsgeNbJ1+vTpBIB++eUXIiIqLS2lOXPmUJMmTcjY2Jjc3d3p7bffpoyMDOkx8fHxNGzYMHJ2diYTExMKCAigpUuXSverR09v3bqVvLy8yMLCgnr37k2RkZEa+05MTKQRI0aQnZ0dWVhYUJcuXWj79u0a6+C+EdAVt1/deojKRz337duXrK2tycrKijp37lxpX1XZtWsXdejQgUxNTcnd3Z3ef/99ysvLq7KO0NBQCgwMJHNzc3J2dqY+ffrQ8ePHydramj744AMiIsrNzaV3332XPDw8yMTEhNzd3Wn69OlUUFAgbWfBggXUokULMjc3JwcHBxoxYoTGaPr7X5P7f77/tC5RFOmHH36gpk2bkqmpKfn7+9Ovv/4q3R8ZGUnBwcFkZWVFjo6O1K1bNwoJCSF/f3968cUXiaj89LnRo0eTmZkZBQQEEFHl91Z+fj69//775ObmRiYmJuTn50c//PCDxoj3qt6PVY1Av19UVBQBoPDwcGlZVTURES1ZsoRatGhBJiYm5OrqSu+88w5lZmZK99+7d4+effZZMjExof79+xNR+YjrgIAAMjMzo0aNGtGwYcPoyJEjJAiCNLq8uqPE1aPP1aoa4f24HrSNK1euSKekmZubU/fu3enYsWOV1nv77bepY8eONarBkHBgM4Nxf5Ax/TVr1iyNENuwYcNDz21/mKSkpCoDT23gwIE0atQoateuHZWUlDzRPu63du1aatKkCZmbm9Pzzz9PaWlpGvcnJiaSj4+PxrKioiIKCAio9AXj3XffrXT6pPqL8Mcff1zpVKv6kpeXRzY2NrR161ZZ9q+LuA+bMaZ3pk+fji1btgAArl+/jhEjRqCgoKBO9vXNN99g8+bNGD16NIyNjWu8vTNnzuDVV1/FrFmzEB4ejqysrEoX2Dh06JA07gIoH3Q4evRoxMTEVNrepUuX8O233yI5OVm6qQcdfvDBB/j222+rHIxZ13799VcEBARgyJAh9b5vXcWBzRjTO1ZWVtJoZKrjy1VaW1tDqVQ+chrT6lq4cCFGjBiB8ePHo1WrVli1ahX27Nmjca5yxcC+dOkSOnfujISEhCq3Fxsbi3bt2sHFxUW6qU/xsrOzQ58+fer9ginp6en4+eef8ddff/GFPx4DBzYzGLNnz+ZrDWuhmzdvYvDgwbCwsICXlxe++OIL6TSsl156Cf7+/tL518uXL4etra00MjskJATt2rWDhYUFWrdujUOHDgEo/12r5/H29vaW/lVfPWrr1q1o0aIFLCws0LFjR+mMBAAoLS3FlClTYGdnB3d3d2mQ14MsXboUQ4YMkc5Jnz17NsaOHYu3334bNjY2cHZ2xvfffy+t7+XlVeV83epWdHh4uHRmBAA0btwYHh4eCA8PB1D+BaTinOVHjhxBz549cerUqUq15ebm4vbt2/D19X1g/YMHD8bSpUtrddT5ozg5OeHWrVvVPuuC/UvmQ/KMMQMmiiIFBgbSq6++SpcvX6bQ0FDy9fWV5ohPS0sjR0dH+vHHHyklJYUcHBykgXsXL14kIyMj+vLLLyk+Pp6+/fZbsrS0pOTkZI0+7DNnzhAAOnPmDBUUFFBkZCRZW1vTmjVr6MqVKzR//nwyNzeX5nj/6KOPyNPTk44cOUInT54kX1/fh/Zht2nTRmPaz1mzZpGxsTH95z//oStXrtD333+vMV92amoqJScnV7plZ2cTEZGVlRXt3btXYx8dO3ak77//nojK5/Ju27ZtlbXgvkFy4eHhJAgCvfbaa+Tm5katWrWiFStWaDymsLCQlEolRUVFPerXxWTGgc0Yk83BgwepQYMGGiO2d+zYQQ4ODtLPK1euJFtbWxowYAD16tVLWv7ee+9VGh396aefUmxsrEZg3z9obNy4cTRt2jSNxw0bNoymTZtGoihSgwYNNOab37179wMDu7S0lBQKBZ04cUJaNmvWLHJxcdG4aIiDg4PGvPIPo1Ao6PDhwxrLunXrRl9++SUREf38888PnGv+/sBesWIFKRQK+vHHHykqKooWLFhAJiYmtGXLFo3H+fj40PLly6tVH5MPn4fNGJNNbGwsMjIyNObwVk/mkpGRAUdHR4wfPx7Lly9HSEgIrly5Iq0XFxeH9u3ba2zvyy+/rNY+N2zYIM1jDwAlJSXo06cP0tPTkZaWpnFpyA4dOjxwW5mZmRBFsdJMXd7e3hoToVhbW0uH9QMCAipNzAOUz2/+66+/wszMrNI84cXFxVK/86FDhzBp0qRHPk+g/FKYgwYNkvrzW7Vqhfj4eCxZskSaCAconzglNTW1Wttk8uHAZozJpqysDP7+/ti+fXul+2xtbQEAeXl50oCqo0ePSn3STzoiu6ysDDNnzsT48eM1lpubm0v/pwpjHSrOzHU/9YCp+6c+reox6m3u2bNHCu+K1F9a3NzcKk1jevfuXbi6uqKsrAynTp3C+vXrH1jT/fXdPxVo8+bNcfjwYY1loihWe1IgJh/+DTHGZOPn54cbN26gQYMGaNasGZo1a4akpCTMmjVLCsNPP/0Ujo6O+OWXX/D+++9L84/7+PhIFydRCwoKqhRm949C9vPzQ1JSkrS/Zs2a4bfffsPevXvh5OQkTSmrdv78+QfW7+joCKVS+VinRXl6emrsW31Tz2ffuXNnaQYwoHxQ3s2bN9G5c2dERESgRYsWj7zgiNrnn3+OZ599VmNZZGQk/P39NZalp6dXeXESpl04sBljsnnuuefg6emJcePG4cKFCzh27BjeeOMNWFhYQKlU4uzZs1i0aBEWLVqEN998E97e3njvvfcAAG+99RaOHTuGuXPn4urVq/j2228RExOjMcIagBRuUVFRyMvLw3vvvYf169djwYIFSEhIwLx58zB37lz4+vpCEARMnjwZn3/+OQ4ePIizZ89K+6uKQqFA69atER0dXWuvydtvv41Vq1bhjz/+QHR0NMaPH4+BAwfC29u70vnXjzJo0CAcOXIEP/74IxISErBkyRL89ddfmD59urTOvXv3cO3aNbRr167WngOrI3J3ojPGDFtCQgL179+fzM3NqUGDBvTOO+9QQUEBlZaWUps2bTSmvjx9+jQpFArat28fERHt3LmTAgICyNTUlNq1a0dHjhwhosoznY0bN45MTEzo559/JiKidevWkZ+fH5mYmFDz5s1pw4YN0roqlYo+/PBDcnBwIGdnZ1q0aNFDR4l//PHHNHr0aOnn+/dNVD6F5+MM6lq+fDk1btyYLC0taejQoZSenk5ERD169KCjR48+8HGoYirVbdu2UatWrcjMzIz8/f1p8+bNGveHhISQh4dHtWtj8hGI+MRUxhh7UgkJCWjfvj3u3LlT6ZrTuuDll19G06ZNNS5Dy7QTBzZjjNXQ2LFj0aNHD7z++utyl/JYMjIy0Lp1a0RFRcHR0VHuctgjcGAzxlgNJScno1+/fjhz5sxDR5Vrm48++gienp5466235C6FVQMHNmOMMaYDeJQ4Y4wxpgM4sBljjDEdwIHNGGM66Nq1axAEAdeuXXvkumFhYQ+9jGXFq5utWLECXl5eD1x34sSJ0pXFiKhGl+Z8VF0AEBMTI12ZrKaKi4vxwQcfwN3dHfb29hg6dChu3bqlsU5oaCiee+45jWWZmZlo2LBhpdd6yJAhla66tmvXLgDlU80eOHCgVupW48BmjDEd1LhxYyQnJ6Nx48Y13tb06dOxZcuWaq07f/58zJ8/H0D5VLHVndf8SU2aNAmff/55rWxr1qxZ2Lp1K9asWYMTJ06gtLQUw4YN05iK9v7JabKysjBo0KAq51q/dOkSVq9ejeTkZOnWu3dvAOVfgqZOnYqSkpJaqR3gwGaMMZ2kVCrh4uKicZGRJ2VlZVVpzvEHsbW1leZ5r+sxy0ePHsXdu3drrYW9YsUKfP311wgODkaLFi3w+++/IyIiAlevXpXWqRjYx48fR/v27ZGXl1dpW8XFxUhKSkKHDh3g4uIi3UxNTQEAzZo1g6enJ/7+++9aqR3gwGaMMZ10/yFxQRCwevVqPPXUUzA1NUW3bt2QlJSk8Zhff/0Vbm5usLKywssvvyxdFaziIXG1jz/+GDY2NnBzc8Mvv/wiLVcfEr927ZoUpIIgICwsDACwdOlSeHt7w8rKCj169MCFCxekx+bm5mL06NGwtraGr6+vxpztVVmyZAmef/55jX1PmzYNI0eOhIWFBRo3boxVq1ZJ999/eFp9mz17NkRRxOrVq6UWcEU5OTlSfQkJCdI0rfv27cMrr7yCzZs3V3pMXFwcBEFAkyZNHlj/4MGDsWTJkoc+x8ci0wxrjDHGauD+63wDoCZNmtChQ4fowoUL5O/vT2PGjCEiotDQUAJAvXr1ogsXLtD+/fvJ0tKSlixZQkSa06kuX76cANCAAQPo4sWLtGLFCjIxMZGmPJ0wYQJNmDCBysrKaPPmzQSAkpOTqbi4mHbs2EEuLi60c+dOio+Pp08//ZQcHR0pMzOTiIhGjx5Nbdq0oXPnzlFISAg1bNiQHhRDoiiSg4MDHTx4UFo2YcIEMjY2pu+++44SEhJo6tSpZG5uTtnZ2URElJycXOXt3r17Ve5j5syZ5OTkRMXFxURUfi32oUOHPvK1JiJav349OTo60qhRo8jFxYU6dOhAe/bs0XjclStXSKFQUFZW1gN+i4+HA5sxxnRQVYG9cOFC6f758+eTj48PEf0/sOPi4qT7n3/+eXrrrbeIqHJgm5mZSfOXExFNnDiRRo4cSUT/D+yK21Xr2rUrLViwQKPOdu3a0YIFCyg7O5uUSqXGXOjqedqrkpiYSADo9u3b0rIJEyZQYGCg9HNOTg4BoBMnTjz8xarCtm3bSKlU0u+//y4tmzp1qsZrqFZVYM+ePZvMzc1p+fLlFBkZSbNmzSKlUkkRERHSOiqVikxNTSvN7/6k+JA4Y4zpCR8fH+n/NjY2la673bRpU+n/tra2KCoqqnI7TZo00ZiqtF27doiNjX3k/mNjYzFjxgxYWVlJt6ioKMTHxyM+Ph4qlQpt2rSR1u/QocMDt6W+jKqTk9NDnyMA6XlW3G/F2zfffKOxjW3btmHEiBGYMmUKXnvtNWn541wN7bPPPsPt27cxceJEtG7dGrNnz0a/fv3w22+/SesoFArY29tXOWDtSRjVylYYYwaNiCCKIoDyfkSFonptASJ65Gk90rqiChBV+HcngMKo2o81FPdPi0r3DQq7f4Da/fc/aD1RFKs15WpZWRnmzZtXKfRsbGxw/fr1Svt82DbVv1uVSqWxvKrHqLcZGRlZ5bYqDqhbv349XnrpJbz11lv4+eefpeUpKSnIysqqdK3wB1GHcUXNmzdHTEyMxjJRFKv99/AoHNiMsQdSB7FCoagUjiqVCgUFBSgoKMC9e/eQl5eHgoIC5Ofna9xKSkogiiJEUZS2J4oiPDw8MHLkSNz6qT+KEiMgKBSAoIBgYg6FmXX5zdymwr9WUJjZQGFuDaWlA4zs3WDk6AkjO1coLWw161aVASQCSmMO9SeQkJCAgoIC6epjZ86cqTLI7n9t/fz8cOvWLTRr1kxa9vLLL2Po0KHo0aMHjI2NERERIQX6+fPnH1hDw4YNAZRfoMTd3b1adVfcb1UOHTqEl156CZMnT9YIawA4fPgwnnnmmWrtBygfAKdQKPDnn39KyyIjI9GyZUvpZ1EUkZmZCRcXl2pv92E4sBljVQZzbm4u7t69i9TUVGRlZVUZxDWhHqEsFt2DmJ9Zo20JxmYwsmtUHuL2bjCybwQjRw+YuPrD1P0pGNm5SutSWQmgUEJQ1Px0KH1VVFSECRMmYPbs2Th+/Dg2btyIU6dOVVrP0tISAHDu3DkEBARg2rRpeO211+Dr64ugoCD89ttv2LBhgzTifPz48ZgyZQqWL1+OwsJCzJ49+4E1NG7cGE5OToiOjq52YD9MWVkZXnnlFQQHB2PmzJm4e/eudJ+Dg8NjHQ4HykeAjxo1Cj169EBQUBDWrl2L48ePaxwSV3cjtGrVqsb1AxzYjBkclUqlEcwFBQVISUlBSkoKUlNTkZqairS0tFqd8KGuUWkRStMSUZqWWOX9gqklTFz8YNLIHyau5TfTxq1g3MALgtK4/HA7iRCUxvVcuXZq06YN3Nzc0KlTJzg5OWH58uVo3759pfVatmyJ3r17IygoCOvWrcPIkSORkpKCzz77DCkpKQgICMDOnTulfudffvkFU6ZMQe/evWFvb493330X06dPr7IGQRDw3HPP4fjx4+jfv3+Nn9PZs2dx48YN3LhxA66urhr3hYaG4vDhw5g1a1a1tzds2DAsXrwYX331FW7cuIGAgACEhIRozBJ3/PhxBAUFSX3tNcVX62JMj6n7lRUKBUpKSpCamqoRzKmpqSgoKJClNm9vb4wfPx43vu6GoisnZakBSmOYerSGWZMOMG/SCWY+QTBxLh+YRaoyQBC4JS6jsLAwvPrqq0hISJC7lCfSs2dPvPrqqxg3blytbI9b2IzpESo/VRMKhQKlpaW4fv06EhMTkZSUpHEIkP1LVYripLMoTjqLnEPlE1woLOxg5h0IsyYdYNa0M8x9gqC0dChvhQMc4PWoR48ecHFxwYEDB6qc8ESbXb58GTdu3MDIkSNrbZsc2IzpMHXfs1KphEqlwp07d5CQkIDExETcvn1bamGz6hMLslEQcxAFMQfLFwgCTNxbwjKgFyye6gNzv25QGJuBVKU8Ur0eLFmyRDqMrku++OILLFq0CMbGtdfNwofEGdMxKpUKSqUSRISUlBQkJCQgKSkJN27cqHTerTbTikPiT0AwNoVZ0y6wCHgWli37wNSzDQRBAVKVch84q1PcwmZMB6hDOjc3F3FxcUhMTMS1a9ceOPEFqztUWozCy2EovByGjM2fQmFpD8unnoNV4AuwbN0fChNzDm9WJziwGdNS6pC+d+8eLl68iJiYGNy+fVvusth9xPws3Dv9N+6d/huCiTksn+oDqw7DYNV2CBRmVhzerNZwYDOmRdQhnZ+fL4X0zZs35S6LVROVFCLvn23I+2cbBCMTWLToBavAYbAKfAFKC1sOb1YjHNiMyUw9YUlpaSliYmIQFRWF69ev1/m1hlndorIS5EfvRX70XqSsfBuWTz0Hm+6vwKrNoPKpVQUBgsCXc2DVx4HNmAzUp18JgoAbN27g/PnziI2N1alBY+wxqMqQH7UH+VF7oLRuAOugsbDr8QZMXP241c2qjQObsXqkbk3n5ubi7NmziI6ORm5urtxlsXqkupeG7H3zkL1vHsyadIRNt5dhEzQWClNLkKji87zZA3FgM1YP1H3Td+/exfHjx3H58mU+5M1QlHgGRYlnkLZuGqw7vAi7PlNh5tGGW92sShzYjNUh9bzdV69exYkTJ3gAGasSlRQi98RfyD3xF8yaBcG+739g1W7ov/Ob88c0K8fvBMbqgPpSkufPn0d4eDgyMjLkLonpiKKrJ5G88CSMHBrD7rl3YdfzLQjGpuWXHuVZ1QwaBzZjtUR9iLuoqAhnzpzBmTNnZLuwBtN9ZZk3kb7+A2Tu+Aq2wa/Dvu80KG2cARCPLjdQHNiM1ZB6IFl2djZOnjyJyMhIlJWVyV0W0xNiQQ6y9v6IrP3zYdvtZTg+/zmUtg0BCNziNjAc2Iw9IXVQ37lzBydOnEBcXBwPJGN1R1WKnLDfkHt8BWyDX4PDkM+gtHYCB7fh4MBm7DGpQzk7Oxv79u1DfHy8zBUxQ0JlJcg+tBg5R/+Ebc834Dj4Uygs7P+diIWDW59xYDP2GERRRHFxMUJDQ3Hu3Dm+fCWTDZUWIXv/AuSELYNdr3fgMPAjKMyt+TxuPcaBzVg1qFQqAMDp06dx9OhRFBcXy1wRY+WopABZe39EduivcOj7PhwGzAQUCj6PWw9xYDP2EOp+6ri4OBw4cADZ2dlyl8RYlagoDxnbvkDO0T/hNOp72HQcAVKV8XnceoR/k4xVQT3P9927dxESEsITnjCdUZZ5E3cXj0bOwUVo8NIvMGvcCkQinwqmBziwGbsPEeHevXvYv38/YmJi5C6HsSdSGH8cNz5vD5tuE9FgxH+hsLDj/m0dx4HN2L9EUURZWRmOHj2K8PBwqd+aMZ1FInKP/om8iE1wGPwJ7J/7DwDi/m0dxYHNDJ768HdcXBx2796N/Px8uUtirFaJhblI/3smck+sgstry2Hq2ZZPAdNB3KnBDJpKpUJxcTE2b96MDRs2cFgzvVZy6yJufNEJaeunQywtAqn4+uu6hFvYzCCpW9WJiYnYsWMH8vLy5C6JsfpBIrL3zUP+PzvQ8NVlsPAPlv4emHbjwGYGR6VSQaVSYe/evYiMjJS7HMZkUZqWiFv/fQa2wa+hweifAGNT7tvWchzYzGCoWxE3btzA9u3bkZOTI3dJjMku58gy5EftgfPEX2HVZgC3trUYBzYzCCqVCqIoYv/+/Th79qzc5TCmVcqy7+DOvMHlre1x8wGFklvbWogDm+k9IsKdO3ewdetWZGVlyV0OY1or58gyFF45AddJG2Di6sfnbWsZHiXO9Ja6r3r//v1Yvnw5hzVj1VByJxY3ZndATuivAADiC9xoDW5hM70kiiIyMjKwceNGpKeny10OYzqFSouQuupd5F88CJfXl0NhasmHyLUAt7CZXrpw4QJ+//13DmvGaiD//A5c/6QVChPCQcQtbblxYDO9IYoiRFHEnj17sG3bNpSVlcldEmM6ryzrNm59+wwyd3wNgA+Ry4kPiTO9oFKpUFRUhPXr1+PWrVtyl8OYfiERGVtno/hGFFzeXAUojfmynTLgFjbTeUSE27dvY8mSJRzWjNWhvHNbcWNOZ5Rl3Qap+AhWfePAZjrv3LlzWLlyJc8Dzlg9KLl1EddntUdh3FHu165nHNhMJ1Xsr969ezdE7ldjrN6I+Vm49WNfZO+bD6D8KBerexzYTOeoVCqUlpZizZo1iIiIkLscxgyTqELa+ulI/m0ioCoFiXz9+LrGowaYThFFEbm5uVizZg0yMjLkLocxg3fv5CqUplyB2/t7/j1fm2OlrnALm+kMURRx48YN/PbbbxzWjGmRooRw3PwyCKrcVL7Gdh3iwGY6gYgQFxeHVatWoaioSO5yGGP3KUm+jBtfdEJJylUeQV5HOLCZ1iMiXLx4ERs3buTBZYxpsbLsO7j51dPlM6Nxn3at48BmWo2IcP78eWzdupVHojKmA8SCHNz+4Tnknd/Bp33VMg5sptUiIiKwc+dODmvGdAiVFiN54QjkhC6VuxS9woHNtNaJEyewd+9euctgjD0JEpH612Rk7PxG7kr0Bo+/Z1opLCwMR44ckbsMxlgNZWz+DCCC4+BP5C5F53FgM61z4MABnDx5Uu4yGGO1JGPL5wDAoV1DHNhMKxARBEHAnj17ePYyxvQQh3bNcWAz2akHlO3YsQPnz5+XuRrGWF3h0K4ZDmwmKyICEWHbtm24cOGC3OUwxuoYh/aT41HiTFaCIGDr1q0c1owZkIwtnyNjx9dyl6FzOLCZrA4cOICLFy/KXQZjrJ5lbPkc2YcW8eQqj4EDm8mCiHDmzBkeDc6YAUtdPRX3IjbxNKbVxIHN6p0oioiPj0dISIjcpTDG5ESElGUvQywugKqMLxjyKBzYrF6pVCrcvXsXmzZt4ulGGTNwgqklPL+OgcLMCnn5+VCpuKX9MBzYrN6oVCrcu3cPa9asQRl/m2bMoCntGsH7xyQoHRpj48aNWLZsGfLz8/mKfA/Bgc3qhSiKKC0txapVq1BQUCB3OYwxGZm4tYDnfy9DNLXGqlWrEBsbi7y8PKxatQqlpaUc2g/Agc3qnPpc67Vr1yIzM1PuchhjMjJv8Qwazz6HYhXwxx9/4Pr169J96enpWLdunfSZwTRxYLN6sXnzZty8eVPuMhhjMrIOGodG7+9Fzr08/P7770hNTa20zvXr17Ft2zYIgiBDhdqNA5vVuf379yM2NlbuMhhjMrIfMBMNX1uOu3dTsGzZMuTk5Dxw3YsXL+LQoUP1WJ1u4KlJWZ0hIpw+fRrh4eFyl8IYk1GDlxbC7pm3cPXqVWzcuBGlpaWPfMzx48fh7u4OHx8fKBTctgS4hc3qiCiKuHLlCvbv3y93KYwxGTWaug32vd5GZGQk1q9fX62wVtu2bRvu3bvHg9D+xYHNap0oisjPz8fWrVt54AhjhkqhQONZp2HVdhCOHj2KHTt2PHbwFhUV4e+//+bPkX9xYLM6sXHjRhQVFcldBmNMDiYW8PruCsy82mPPnj0IDQ194k0lJydj3759tVic7uLAZrWKiBAaGsojwhkzUApbF3j/dA1Kh8bYtGkTIiIiarzNiIgIXLp0yeAPjXNgs1qjUqlw7do1nDhxQu5SGGMyMHFrAa/v4kGm1li9ejUuXbpUa9vevn07cnJyDDq0ObBZrRBFEcXFxdi8eTP3NzFmgMyb95QmRPnzzz9x7dq1Wt1+SUmJ1J9tqJ8xHNisVigUCmzevBn5+flyl8IYq2fWnceg0fQQaUKUlJSUOtlPSkoKdu/ebbCTqnBgsxojIhw/fhyJiYlyl8LYA5WoCAO3izh99/+ts2O3CYN3iGi1WsTgHSKO3Hpwy00lEn48J+Lpv0W0XSNiapiI9ML/r7//OqHrBhHBG0Ucvqm5nRd3i7iUoZ+tQvsBM9DwjZVISUl95IQoteH8+fOIjo42yEPjHNisRlQqFe7cuVOjUaCM1bViFWHaUcKV7P8vu55LmBxKGNZUwO4hAoY2FTAplHArr+pg/e0isCcJmBcsYOMAATklwIxj5euqRMJnpwgzAgW8107ARyf+f9j2yC1CA3OghaP+tQobvLQQTsO/QWJiIlasWIHCwsJ62e+uXbuQlZVlcJfj5MBmT4yIUFZWho0bNxrkt12mG65mE0bsJty4p7n8bgEwwheYGCCgsbWAlwMEWBgB0elVb0clAh91ENDBRUAzOwEv+Qs49+9U2FnFQHYx0NcT6O9V/v/Mf89qXBRFmNxa/8LadepW2Pd6G1FRUVi3bt1jTYhSU6WlpVi/fj1EUTSo/mwObPbEBEHAtm3b6vwQGGM1ceYu0MkF+Lu/Zmh2chHwScfyj8BSkbDxCqFEBFo5Vb2dyW0E9PYs30ZGYfn6HV3K77M3BcyNgEuZwMUMwMIIsDMFjt4mOOpb6/rfCVGs2w7GsWPHsH37dlm+sKenpxtcfzbPJc6eiCiKOHfuHC5fvix3KYw91Bh/AYD6Q71ya+x6LqHfNoKKgPfbCXC3engALIgUsSgKsDUB1vUrX1epEDC9HTB2L0EQgM86CVAqBCyOEvF5Jz0KFBMLeH0dDWMnL+zduxdnzpyRtZyoqCi0bNkSXl5eUCqVstZSH7iFzR6bKIrIzMzk2YeYXnAwAzYNEPB5JwG/RBL2XX/4IdYhTQRsGiCgiyvwygFCXkn5+uOaCzg9WsDpUQJG+go4fptgbwZ42QBTw0T02CTi2wjdPYRbPiFKEpQOHti0aZPsYa22a9cunX1NHxcHNntsgiBg+/btBjfgg+knaxMBLRwFjPUX8KIPsDr24R/+njYCWjoJ+L6bgCIVsP/G/++zMhZgaVzeol4UXd53vfoyoCIg5HkBZ1M019cV/58QxabWJ0SpqezsbISGhhpEaHNgs8ciiiIiIiJw69YtuUthrEauZBHOpmh+yDe1E5BVXPX6oTcJKfn/X99UKaCxFZBVxZT5J+8QbE2AAEcB/6QSglwFmBkJ6OwKnEvRrWAx9++BxrPP1tmEKLXh1KlTSE1N1fvBrxzYrNpEUURBQQFfWJ7phdBbwKcnNWfNiskgNLGtev3vzhK2VZhqIK+UcC0XaGpXed1FUYTJbcpb2grh/z3nKrGqXnTtZd15FBp9sA859/LrdEKUmiIi7NixQ+8HoHFgs2pTKBTYtWsXSkpK5C6FsRob3ARIKwR+/IdwLZew5jJhRyLwZsvyD/0SFSGtkKASyyN2rL+APy4SjtwiXMkifHCM4GEDdHfT3O6pZIK1CfDUvyPDWzoJCLlW/pjQW0DbBroRKvb9PkDDN1YhJSUVf/zxh9afDXLnzh2cPn1ar1vZHNisWlQqFS5fvoy4uDi5S2GsVrhYCvijt4CIu8CQHeWBPT9YQMC/QXs+Dei6gZBcUL7+WH/gtacEzA4nDN9NEAAseUaA4r5W3eIowqQK512/5F9+yteovYSODYG+XvX0BGugwbgFcHrx/xOiFBQUyF1StYSGhiI/P19vQ1sgQ+ipZzVWUlKChQsX4t69e49embFq8Pb2xvjx43Hj624ounJS7nLYv1zf3QLrdkMQGRmJnTt36lz4+fr6YvTo0XKXUSe4hc0eSX2Naw5rxvSYQoHGn4fDut0QHD9+XLYJUWoqPj5eb6+dzYHNHkoURWRkZGjNOZeMsTpgYgGv/8bDzDsQe/fu1fmBpXv37kVZWZnenerFgc0eSqFQYPfu3Xr5bZUxBihsnMsnRHHUrglRaiIvLw/79+/Xu1HjHNjsgVQqFWJiYrTyvEvGWM2ZuDaH1/dXtHJClJo6d+4cbt26pVcTPHFgswcSRRH79++XuwzGWB0w9w9G4znnUKwStHZClJratWuXXs0xzoHNqkREOHr0KHJzc+UuhTFWy8onRNmP3LwCLFu2TGsnRKmplJQUxMTE6E0rmwObVUJEyMvLw6lTp+QuhTFWy+z7TZcmRFm2bBmys7PlLqlOhYaGQqHQj6jTj2fBat2RI0f05lspY6xcg7Hz4TTiWyQlJenUhCg1kZGRgaioKL34POPAZhrUrevz58/LXQpjrBa5TtkM+96TER19AWvXrkVpaancJdWbsLAwuUuoFRzYrJIjR47waVyM6QuFAo0/OwXr9s/j+PHj2LZtm8H9fefk5OCff/7R+efNgc0kRIR79+5x65oxfWFiBq//XoZ5044ICQnR+QlRauLo0aMc2Ey/cOuaMf2gsHGG94/XoHT0wqZNm3D69Gm5S5JVXl4eIiIidPrzjQObAfh/6zoyMlLuUhhjNSRNiGJmi9WrVyMmJkbukrTC8ePHdXrwGQc2k3DrmjHdZ+7XHY3nnEOJqMDy5cv1ckKUJ1VQUIBTp07p7OccBzbj1jVjesKq40g0+uAAcvMK8Pvvv+Pu3btyl6R1Tp48qbMj5DmwGQBuXTOm6+z6vg+Xt1YhJdUwJkR5UsXFxThx4oROft5xYBs4bl0zpvsajJ2HBiP/i6SkawYzIUpNhIeHo7i4WOcuv8mBzbh1zZgOc528Cfa9pxjkhChPqrS0FMeOHZO7jMfGgW3AiAi5ubncumZMRzX+7CSsA4fixIkTBjkhSk38888/KCsrk7uMx8KBbeCOHTvGf+SM6RoTM3h+Fw/zpp0QEhKCgwcPyl2RzikuLkZkZKROnebFgW3ASktLER0dLXcZjLHHoJ4QxciJJ0SpqdOnT+vU9bI5sA2USqVCVFQU93cxpkOMXf3g9V35hChr1qzhCVFqKCMjAwkJCTrTyubANlBKpRLnzp2TuwzGWDWZ+XaFxxfnUULlE6IkJSXJXZJeCA8P15lWNge2ARJFEXfu3EFKSorcpTDGqsGq40i4zTiE3HyeEKW2Xb16FVlZWTpxihcHtgESBAERERFyl8EYqwa7Pu/B5a1VSE1L4wlR6oiujAPgwDZApaWl3PfFmA5wGjMXDUZ9j6Ska1i+fDlPiFJHoqKidKIfmwPbwPBgM8Z0g+ukDbDv/S4uXLjIE6LUsaKiIly6dEnrQ5sD28DwYDPGtF/jT0/AusMLOHnyJLZu3cpzJdSDc+fOaf3gMyO5C2D1RxRFpKSk8GAzxrSViRm8voyGScOm2LdvH8LDw+WuyGDcuHEDGRkZcHBwgCAIcpdTJW5hGxAebMaY9lJYOcH7xyQonbywefNmDmsZnD17VqtHi3NgG5DS0lJcvHhR7jIYY/cxdvWD1w8JgLk91qxZw3+nMomKiuLAZvJTqVSIjo7mgSuMaZmKE6L8+eefPCGKjAoLCxEXF6e1g884sA0EDzZjTPtYdRgOtxmHcK+gEMuWLeMJUbRAbGys1g4+40FnBoCIkJqayh8GjGkRuz7/gdPI75GamoZVq1bxOdZaIj4+HiqVSitDm1vYBoCIcOnSJbnLYIz9y2n0T3Aa+QOSkq7hzz//5LDWIiUlJUhKStLKU+k4sA2AQqFAfHy83GUwxgC4Tvob9s9NxcWLPCGKtoqNjdXKU7s4sA3AvXv3+HA4Y1rA/ZNjsO4wHKdOneIJUbTY5cuX5S6hShzYek6lUmntm48xg2FiBq/v4mDhE4R9+/bhwIEDclfEHqKgoAA3b97Uui9UHNh6TqlUIi4uTu4yGDNYCisneP+QBKWTN0+IokNiY2PlLqESDmw9V1paimvXrsldBmMGyaihT/mEKBb2WLt2LU+IokNiY2OhUGhXRGpXNaxWiaKIq1evau0kAIzpMzOfIHh+GYUSUmD58uVITEyUuyT2GHJycpCSkqJVM59xYOsxQRD4cDhjMrDsMBxuM0OlCVGSk5PlLok9gUuXLnFgs/pz5coVuUtgzKDYPfcfuL61Fmnp6Vi2bBmysrLkLok9IW07LM4znekpIsKtW7d4QgbG6pHTqB9h99xUXLt2DX///TdKSkrkLonVQFpaGrKysmBvby93KQC4ha23iIhP52KsHrm+sx72ff6DmJgYrFmzhsNaT1y6dElrxgFxYOsphULB/deM1RP3T47CuuOLCA8Px5YtW7Tu/F325C5fvqw184rzIXE9lZ2djYyMDLnLYEy/mZjBc04kTF18sH//fpw6dUruilgtu3PnDsrKymBkJH9ccgtbD6lUKq086Z8xfaKeEMXYuSm2bNnCYa2nRFFEcnKyVowW58DWQ0qlkidLYawOlU+IchWwsMeaNWtw4cIFuUtidej69eta0c3Bga2nbt26JXcJjOml8glRIlEKI54QxUDcvHlTK/qx5T8oz2pdVlYWn87FWB2wbD8MLm+vw728PPz11198jrWB0JYGELew9YxKpeLD4YzVAbvn3oXrO+t5QhQDVFBQgOzsbLnL4MDWNwqFQmu+DTKmL5xGfg+nUT/h+o0bWL58OfLz8+UuidWza9euyX4+Nge2nhEEATdv3pS7DMb0hsvb62DfdxouXbqE1atX84QoBurWrVuyT1PKfdh6pri4GGlpaXKXwZhecP/4CCx8u+LUqVPYv3+/3OUwGd28eROCIMhaAwe2HiEi3L59W+4yGNN9Ribw/DIKpq6+PCEKA1A+r3hJSQlMTExkq4EPiesRURRx584ductgTKcprBzg/dM1GDdsxhOiMAkR4ebNm7JOoMKBrUeUSiUHNmM1YOTcFF4/JAIWDli7di1PiMI03Lx5U9YJVPiQuJ5JTk6WuwTGdJJZsy5wm3EQJSrCquXL+W+JVSL3BCoc2HqkuLhYK84VZEzXWLYf+u+EKPk8IQp7oFu3boGIZBt8xofE9QQRcYuAsSdg9+wUuL7zN9IzMnlCFPZQJSUlyMnJkW3/3MLWE6Io8ghxxh6T08jvYNdnGq5fv47169fzOdbskdLT02FraytLK5tb2HpCqVQiJSVF7jIY0xkub6+Bfd/3eUIU9lgyMzNlG3jGLWw9wofyGKse9YQo4eHh2Ldvn9zlMB2SlZUlWx82B7Ye4QFnjD2CkQk8v4yEqasfT4jCnkhmZqZsU5RyYOsJlUqFvLw8uctgTGsprBzg+fVFKK0bYOvWrYiOjpa7JKaDMjMzZds3B7aeuHfvntwlMKa1jJybwmP2WZCxOdauXYuEhAS5S2I6Ss4jmTzoTA8Qkazf+hjTZqZNOsHzq2iUCsZYsWIFhzWrkbKyMtmOZnJg6wFRFHnAGWNVsGz/PNw/PoK8wmIsW7aMp+5ltSIjI0OW/XJg6wkecMaYJrtek+D6zgakZ2Ti999/56NQrNZkZGRApVLV+365D1sPKJVKDmzGKnAa8V/Y9X0fN27cwLp16/gca1ar5Dq1iwNbT8g5XR5j2sTlrTWw7jQSMTEx2LZtmywtIabf5Dq1iwNbT3ALmzHA/aMwWPh14wlRWJ2Sq3uFA1sPqFQqPq2LGTYjE3jOOQ/TRv44cOAATp48KXdFTI/JNciXA1sPcFgzQ6awcoDnVxegtHHmCVFYvSguLkZRURHMzMzqdb88SlzH8TnYzJAZOTeB1/cJgKUj1q5dy2HN6o0c52JzC1vHiaLI/dfMIJk26QT3Dw+jREVYvWIFn2PN6lVhYWG975MDWw/wHOLM0Fi2HQKXSX8jL78Af/31Fx9lYvWuoKAARFSvp3dxYOsBPseUGRK7XpPgNOZnpKWnY9WqVcjPz5e7JGaACgsLIYoilEplve2TA1vHCYKA0tJSuctgrF44Df8Gdv0/4AlRmOyKiorqfZ8c2DpOoVBwYDOD4PLmalh3HoVLly5h69atPCEKkxX3YbMnwoHN9J37h4dh4R/ME6IwrVFUVFTv05NyYOsBDmymt4xM4DnnH5g2ao6DBw/ixIkTclfEGIDywK7v6Uk5sPUABzbTRwoLO3h+cxFKm4bYtm0boqKi5C6JMYkcn7sc2HqAA5vpG6MG3vD44h+QsTnWrVuHq1evyl0SYxo4sNkT4cBm+sS0SQe4fxjGE6IwrcaBzZ4IBzbTF5ZtBsFl8kaeEIVpPQ5s9kQ4sJk+sO31DhqMmYf0jAysWrWKZ/BjWo0Dmz0RDmym6xyHfw37/jNw48YNrF+/HsXFxXKXxNhDcWCzJ8KBzXSZ04jvYN6sC0+IwnRKWVlZve+TA1sPyPHGYay2WPgE4fTp0wgJCZG7FMaqrb4nTQE4sHUet66ZrsrJyUFGRgbOnz/PE6IwnWNkVP/xyYHNGJNFZmYmFi5cKHcZjD0RY2Pjet9n/c6rxmqdHG8axhgzdHK0sDmw9QCHNmOM1S8ObPZETExM5C6BMcYMCgc2eyIc2IwxVr84sNkT4cBmjLH6xYPO2BPhwGaMsfrFLWz2RDiwGWOsfhkZGYGI6nWfHNh6gAObMcbqFwc2eyJ8WhdjjNUvY2NjDmz2eIiIW9iMMVbPuA+bPTZRFDmwGWOsnvEhcfZEOLAZY6x+GRsb1/sVuziwdRwfEmeMsfpnZWXFgc0ejyAIHNiMMVbP7O3toVDUb4RyYOs4hUIBCwsLuctgjDGDYmtrW+/75MDWcYIgwN7eXu4yGGPMYCiVSlkaShzYekCOb3qMMWao5PrM5cDWA+bm5rKcE8gYY4aIA5vViI2NjdwlMMaYQeDAZjXCh8UZY6x+2NraQqVS1ft+ObD1ABFxYDPGWD3hFjZ7YqIows7OTu4yGGPMINjZ2UGpVNb7fjmw9YAgCHBwcJC7DMYYMwhynUrLga0HFAoFGjRoIHcZjDFmEKytrWXZLwe2nuAWNmOM1T1LS0tZDocDHNh6w8TEhKcoZYyxOibnAF8ObD3i6OgodwmMMabX5Bzgy4GtRziwGWOsbrm6uspyDjbAga03VCoVBzZjjNUxd3f3er+sphoHtp5QKBRwdXWVuwzGGNNrrq6uEARBln1zYOsJQRDg7u4udxmMMaa3HBwcYGpqKtv+ObD1iKmpKZycnOQugzHG9JKbm5us++fA1iNExK1sxhirI40aNZJtwBnAga1XRFHkwGaMsTri5uYm24AzgANbryiVSnh4eMhdBmOM6R1BEGQdcAZwYOsdJycnmJiYyF0GY4zplQYNGsDIyEjWGjiw9YwgCGjUqJHcZTDGmF5p1KgRiEjWGjiw9YwoimjcuLHcZTDGmF5xc3ODKIqy1sCBrYd44BljjNUuNzc32a7SpcaBrWcUCgW3sBljrBYplUo4OzvLXQYHtj4yNzeHvb293GUwxpheaNiwoeyta4ADW2/xYXHGGKsdjRs3lr3/GuDA1ksqlYoDmzHGaomPj4/cJQDgwNZLSqUSXl5ecpfBGGM6z8jICJ6enrLOcKYmfwWsTjg7O8PW1lbuMhhjTKd5enrKPmGKGge2niIi+Pn5yV0GY4zptGbNmsl6wY+KOLD1FBGhefPmcpfBGGM6zdfXVytGiAMc2HpLoVDAw8MDZmZmcpfCGGM6ydbWFg4ODnKXIeHA1mMKhUJrRjcyxpiu8fHxkX3+8Io4sPWYKIp8WJwxxp6Qv78/BzarHwqFAs2aNdOa/hfGGNMVpqam8PLy0orTudS0pxJWJ4yNjdGkSRO5y2CMMZ2ijY0dDmw9p1Kp+PQuxhh7TP7+/loxHWlFHNh6TqlUcj82Y4w9BqVSCV9fX606HA5wYBsECwsLnlucMcaqycvLCyYmJnKXUQkHtgFQqVTw9/eXuwzGGNMJLVq00JrZzSriwDYAfFicMcaqx9jYGC1bttS6AWcAB7bBcHBwgJOTk9xlMMaYVgsICNCai33cjwPbQIiiiHbt2sldBmOMabX27dtr1WQpFXFgGwiFQoG2bdtq7TdHxhiTm5OTE9zd3bVudLiadlbF6oSZmRlatGghdxmMMaaV2rVrp5WDzdQ4sA2IKIro0KGD3GUwxpjWUSqVaNu2rVYONlPjwDYgCoUC7u7ucHZ2lrsUxhjTKn5+flp/OWIObAOjUqkQGBgodxmMMaZV2rdvr3VTkd6PA9vAKJVKtG7dGsbGxnKXwhhjWsHOzg5NmjTR2sFmatpdHasT6okBGGOMAW3bttX61jXAgW2wOnbsKHcJjDEmO0EQ0K5dO61vXQMc2AZJEAQ0bNgQjRo1krsUxhiTVbNmzWBlZSV3GdXCgW2gePAZY4xp/7nXFXFgGyilUomWLVtq/WkMjDFWV2xsbODr66vV515XxIFtwJRKJVq1aiV3GYwxJouuXbvKXcJj4cA2cJ06dZK7BMYYq3fW1tY6M9hMTXcqZbVOEAQ4ODjw/OKMMYPz9NNPQxAEuct4LBzYBk4URfTo0UPuMhhjrN5YWVkhMDBQp1rXAAe2wVMoFGjQoAH8/PzkLoUxxupFUFCQzrWuAQ5shvJWds+ePeUugzHG6pylpSU6dOigc61rgAObobyV3bBhQ/j4+MhdCmOM1amgoCCdDGuAA5v9i/uyGWP6zsLCQmdb1wAHNvuXQqFAo0aNuJXNGNNbXbp00ZlJUqrCgc0koiji2WeflbsMxhirdebm5ujUqZPOtq4BDmxWgUKhgLOzM5566im5S2GMsVrVuXNnnW5dAxzY7D6iKKJXr146/S2UMcYqMjMzQ+fOnXX+c023q2e1TqFQwNbWFm3btpW7FMYYqxWdO3eGkZGR3GXUGAc2q1LPnj314g3OGDNstra2ePrpp3W+dQ1wYLMqCIIACwsLvjAIY0zn9e3bVydnNasKBzarkiAICA4OhrW1tdylMMbYE2nWrBn8/f11frCZGgc2eyCFQoH+/fvLXQZjjD02pVKJAQMGQBRFuUupNRzY7IGUSiX8/f3h6+srdymMMfZYnn76adja2upF37WaQEQkdxFMe4miiPz8fPzyyy8oLS2Vuxy9lpOTg127duH69eswNzdH586d0aVLFwBASkoKdu3aheTkZDg4OKBfv37w9vautI2kpCSsXLmyyu3/5z//gY2NDXbs2IFLly6hUaNGGD58OKysrKR9bNu2DW+88Ybe9Pkxw2RnZ4dJkybp3cBZ/Xo2rNYpFApYWlqiR48eOHDggNzl6LWNGzfC1tYWb775JtLS0rB582bY2dnB29sbf/31F/z8/PD8888jOjoa69evx5QpU6SwVWvcuDHef//9Stu1sLCAnZ0dLl++jGvXruH111/HwYMHcfz4cfTt2xcAcOTIEXTv3p3Dmum8fv366eX7WH+OFbA6o1Ao0LlzZzRs2FDuUvRWYWEhbt26he7du8PR0RH+/v5o1qwZEhMTERkZCRMTEwwcOBCOjo7o2bMnHB0dcefOnUrbMTIygrW1tXS7du0aUlNTMWjQIABAeno63N3d0aBBA/j4+CA9PR0AkJqaiqysLPj7+9fr82astvn6+sLX11dvBppVxIHNqoWIMHjwYL381qoNjIyMYGxsjMjISKhUKqSnp+PGjRtwdXXFtWvX4O/vr9EX98YbbzxybIFKpcLhw4fRrVs3WFpaAig/JzUtLQ1lZWVITk6Gra0tAG5dM/1gZGSkdwPNKuJD4qxalEolGjVqhPbt2+Ps2bNyl6N3jI2N0b9/f+zZswfh4eEgIrRp0wbt2rXD6dOn4ebmhh07diAuLg52dnbo06cPPDw8HrrNmJgYFBUVoWPHjtKy5s2bIyIiAl9//TVsbW0xfvx4pKWlITMzk1vXTOd169YNVlZWejXQrCIedMaqjYhQWlqKX375BXl5eXKXo3cOHDiAnJwcdOnSBampqdi7dy8GDhyI0NBQFBQUoHPnzvD398fFixdx5swZTJ48WWohV2X58uVwd3dH7969K92Xl5cHCwsLKBQKbNq0CQEBAbCyssLOnTshiiL69euHpk2b1uXTZaxWOTg44J133tHLQ+Fq+vk1hNUJQRBgZGQkDVJitScxMRH//PMPhgwZAjc3N7Rt2xZdu3bF0aNHoVAo4Orqip49e8LV1RW9e/eGo6MjoqKiHri9vLw8XL9+Ha1ataryfnUrJC0tDenp6fD398f27dvRs2dPDBs2DFu2bOGzAphOMYQ5Iziw2WNRKBQICAjg1lctu3PnDhwcHGBsbCwtc3FxQXZ2NqytreHk5KSxvqOjI3Jzcx+4vYSEBNjb2z9yoODRo0fRvXt3FBYWIj09Hc2aNUOjRo0AABkZGTV4RozVnxYtWqBp06Z63boGOLDZExBFEQMHDtS7cxzlZG1tjczMTJSVlUnL0tPTYW9vD3d3d9y9e1dj/fT0dNjZ2T1we7du3ULjxo0fus/09HSkpaWhefPm0mAzdQ+ZKIrg3jKmC6ysrDBo0CCDeL9yYLPHplAoYGNjg549e8pdit7w8/ODUqnEjh07kJ6ejri4OBw7dgydOnVCYGAgUlJSEBoaioyMDBw+fBhZWVnS4e7i4mLk5+drbC81NRUNGjR46D7VrWtBEGBubg4HBwecO3cOFy9eBFDeimdM2w0dOhTGxsYGcYYDBzZ7IgqFAkFBQWjWrJncpegFMzMzjB8/Hnl5efj9998REhKC7t27o3379rCzs8NLL72E+Ph4LF68GPHx8RgzZgxsbGwAACdPnsRvv/2msb38/HyYm5s/cH8ZGRlITU1F8+bNpWWDBg3C6dOnsW/fPgwZMgQmJiZ182QZqyWdOnVCkyZN9P5QuBqPEmdPTBRFFBcXY8mSJbh3757c5TDGDIizszPeeOMNgwlrgFvYrAYUCgVMTEwwfPhwgzgcxRjTDkql0iA/dziwWY0olUo0btwYPXr0kLsUxpiB6NWrFxwdHfV2gpQHMaxny+qEIAjo1q0bmjRpIncpjDE95+vriy5duhhcWAMc2KyWEJHGpRoZY6y22djYYOjQoQZxCldVOLBZreD+bMZYXVIoFHjxxRcN5hSuqnBgs1qjVCrh4eGB4OBguUthjOmZZ555Bm5ubgY1Kvx+HNisVgmCgO7du8Pb21vuUhhjesLHxwdPP/20wbas1TiwWa1T92err8HMGGNPytbWFsOGDdPba1w/Dg5sVusUCgVMTU3xwgsvGPw3YsbYkzM1NcW4ceNgbGxskKPC78evAKsTSqUSXl5ePN84Y+yJKBQKjBo1Cg4ODgbdb10RBzarM+rzs9u1ayd3KYwxHTNo0CB4eHhwy7oCfiVYnSIiDBw4ED4+PnKXwhjTEd27d0ebNm04rO/DrwarU+o+7BEjRsDNzU3mahhj2q5ly5bclfYAHNiszgmCAIVCgbFjx8LBwUHuchhjWsrT0xNDhgwx2JnMHoUDm9UL9UxoL730Ep/uxRirxNHREaNHj4YgCHx2yQNwYLN6o1QqYW1tjbFjx8LY2FjuchhjWsLCwgIvvfQSjIyMuN/6IfiVYfVKqVSiYcOGGDFiBP9hMsZgZGSEMWPGwMrKik/fegT+xGT1TqFQoGnTphg4cKDcpTDGZDZs2DC4urpyWFcDBzaThSAIaNu2LXr06CF3KYwxmfTu3Rv+/v58tK2a+FVisgoODuaJVRgzQD179kRQUBAPMHsMHNhMVuqJVfz8/OQuhTFWT5599ll0795d7jJ0jkB8whuTmfoqPJs2bUJsbKzM1TDG6lKfPn3QuXNnucvQSRzYTCsQEYgIW7ZsQUxMjNzlMMbqQP/+/dGhQwe5y9BZHNhMa6jfitu2bUN0dLTM1TDGaosgCBg4cCDatm3LfdY1wIHNtIr67bhjxw5ERkbKWwxjrMYEQcCQIUPQqlUrDusa4sBmWoeIIAgCdu3ahXPnzsldDmPsCSkUCgwdOhQBAQEc1rWAA5tptYMHD+LEiRNyl8EYe0wKhQLDhw+Hv78/h3Ut4cBmWu/kyZM4cOCA3GUwxqpJqVRixIgRaNasGU+KUos4sJlOiIyMxI4dO/iye4xpOSMjI4waNQre3t4c1rWMA5vpBCJCfHw8Nm7cCJVKJXc5jLEqmJubY+TIkWjcuDGHdR3gwGY6QxRF3Lx5E+vWrUNxcbHc5TDGKmjQoAHGjh0La2trDus6woHNdIooisjOzsbatWuRkZEhdzmMMQC+vr4YPnw4FAoFX3WrDnFgM52jUqmgUqmwZcsWxMXFyV0OYwata9eueOaZZwCAR4PXMQ5sppPU52ofOXIEYWFhcpfDmMExMjLC888/j4CAALlLMRgc2EynERGuXLmCLVu2cL82Y/XExsYGo0ePhrOzM/dX1yMObKbz1P3a69atQ3p6utzlMKbX3N3dMXr0aJiamnJ/dT3jwGZ6QaVSQRRFbN68mfu1GasjrVu3xqBBgyAIAresZcCBzfQG92szVjcEQUDv3r3RpUsX6e+M1T8ObKZ3iAhXr17F5s2buV+bsRoyMzPD8OHD0aRJEw5qmXFgM70kiiJycnKwdu1a7tdm7Al5eXnhhRdegIWFBR8C1wIc2Exvqfu19+3bx5fpZOwxKJVK9OzZE0FBQSAiDmstwYHN9Jq6vy0pKQnbt29HTk6O3CUxptWcnJwwfPhwODs78yFwLcOBzQyCena0ffv24Z9//pG7HMa0UmBgIPr06QNBEPiULS3Egc0Mhrq1nZiYiO3btyM3N1fukhjTCjY2Nhg8eDCaNm3Ko8C1GAc2Mzjc2mbs/9q2bYu+fftCqVRyq1rLcWAzg6RuRSQkJGDHjh3c2mYGh1vVuocDmxk0dWs7JCQE58+fl7scxuoFt6p1Ewc2M3jc2maGokGDBujXrx+8vb25Va2DOLAZ+5e6tX3w4EGcO3cOoijKXRJjtcLCwgI9e/ZE+/btIYoit6p1FAc2YxWo/xyysrJw4MABXL58WeaKGHtySqUSHTt2RI8ePWBkZMQToOg4DmzGqiCKIhQKBW7evIl9+/bh9u3bcpfE2GPx9/dHnz59YGtrCwB8+FsPcGAz9hDq4I6JicHBgweRnZ0td0mMPVTDhg3Rr18/eHp6Su9fph84sBmrBpVKBQCIiIjA0aNHUVhYKHNFjGmytLTEM888g7Zt23I/tZ7iwGbsMYiiiNLSUhw9ehSnT5+WgpwxuSiVSnTp0gXdu3eHUqnkFrUe48Bm7DGp/2Tu3buHAwcO4OLFizJXxAyRIAgICAjAs88+CxsbG+6jNgAc2Iw9IfV5rHfv3sWBAweQmJgod0nMABgbG6NNmzZ4+umnYWtry/3UBoQDm7EaUn9gpqSk4NixY7h06RL4z4rVNgsLC3To0AGdO3eGqakpAB75bWg4sBmrJergzsnJwYkTJ3D+/HmUlZXJXRbTcXZ2dujSpQvatWsHhULBrWkDxoHNWC1T/0kVFRUhPDwcERERPKqcPTZXV1cEBQUhICAARMRBzTiwGatLoiiCiBAdHY0zZ87g7t27cpfEtFyTJk3QtWtXeHt7Q6VS8elZTMKBzVg9UH/w3rx5E+Hh4bh8+TLPVc4kCoUCLVq0QLdu3eDs7MxBzarEgc1YPVL3c+fl5eH06dOIjIxEXl6e3GUxmbi7u6NVq1Zo2bIlzMzMeMQ3eygObMZkQETSaWE3btzAhQsXcOnSJe7rNgAODg5o2bIl2rRpAzs7O25Ns2rjwGZMZqIoQhAEEBESEhJw4cIFxMXFoaSkRO7SWC0xNzfHU089hdatW8PNzU36nfNpWexxcGAzpkXUh0TLysoQHx+PCxcu4MqVKzwFqg4yMjKCr68vWrdujWbNmklfyviQN3tSHNiMaSn1odKSkhJcunQJFy9eRFJSEg9W02KCIMDDwwOtWrXCU089BRMTE+6XZrWGA5sxHaAO78LCQly8eBFxcXG4ceMGSktL5S7N4FlYWKBp06Zo1qwZfHx8YG5uzv3SrE5wYDOmY9RhIIoikpOTkZiYiKSkJNy8eZNnVqsHgiDAzc0NPj4+8PHxgYuLCwRB4JBmdY4DmzEdRkTStY9VKhVu376NpKQkJCUl4datW9z3XQsEQUDDhg3h7e0NLy8veHl5SYe6eeAYq091FtgTJ04EAKxYsaJWt3vt2jV4e3sjKSkJXl5eAICkpCT06dMH8fHxtbovQyEIAkJDQ9GjR49a33ZMTAwmT56M0NDQWt3u66+/Djc3N8yePVtadvXqVUyaNAknTpyAg4MDpkyZgg8++EDjcbNmzYKpqSk+/vhjadnx48cxfvz4SlfbsrOzQ05Ojsaye/fuwcTEBJ07d0ZISAicnZ1r9XnVVMUALysrw61bt6QAv337Nvd/V4NSqUSDBg3g6ekphbSpqan02nF/NJOLUV1teP78+XW16UoOHTqEXr161dv+9E1ycjIcHBzqZNuTJk3CrFmzanWb33//PZYtW6axXVEUMWDAAHTo0AHnz5/HlStXMHr0aLi5uWHMmDHSeocOHcJPP/0k/XzhwgUMHz4cZmZmGvu4ffs2cnJykJCQAAsLC2m5paUlBEHAlClTMGPGjFr/QlpTgiBIh2WNjIzg6ekJDw8P9OzZE6Wlpbh16xZSUlKQlpYm3YqKimSuWj42NjZo2LAhGjZsCGdnZzRq1Aj29vZQKBSVApqDmsmtzgLb1ta2rjZdyaFDh/DCCy/U2/70jYuLS51s9+jRo7h79y569uxZK9vLzc3FK6+8gsOHD6Nx48Ya96WkpKBNmzZYsmQJrK2t4ePjg169euH48eNSYOfl5SEuLg6BgYEAgKVLl2L69Olo0qRJpZZ0bGwsXF1d0aRJkyprGTt2LN5//31cv34dnp6etfL86kLFQ7bGxsbw8vKCh4cHFAqFtDw/Px+pqalITU3VCHJ9msTFxMQEzs7OUji7uLjA2dlZukxlVa1nDmimbar9jrx27RoEQcDu3bvh5eUFKysrTJ06FRcvXkRgYCAsLS0xcOBA3Lt3D0D5IfGJEyeCiBAcHIxnnnlG2tasWbPg4eEhrbt161a0aNECFhYW6NixI44cOSKtW1paiilTpsDOzg7u7u7YvXu3Rl1EhLCwMPTs2VOqccuWLWjatCnMzMwwcOBAZGZmSuufOnUKXbt2haWlJby9vfHrr79K902cOBHTpk3DyJEjYWFhgcaNG2PVqlUPfE3CwsLg5eWFt99+G7a2tvjuu+8AlAeBt7c3rKys0KNHD1y4cEF6zOHDh9GmTRuYmZmhSZMmWLp0qXSfIAj4448/0LRpU1hbW2Ps2LEa01bWpPZH7TcsLAy//vqr1M2g9ttvv8HHxwcAUFxcjKlTp8LJyQlOTk4YN26cxmt7vyVLluD555+vdo3qcLn/pj7snZSUhKKiIvzzzz+VgtTV1RV///03rK2tQUQ4ceIEjh49qnGY/+jRowgKCpJaoHv37sXKlSvx3nvvVar90qVL8PX1feBzMzExQe/evTVeR12gboFX7He1tLSEl5cXAgMDMWDAALz88suYMWMGPvjgA0yYMAH9+vVDYGAgmjRpAmdnZ40jDtpAEARYWlrC2dkZTZo0QatWrRAUFITevXtj1KhReO+99/DRRx/h1VdfxYABA9CuXTs0btxYCmsAfNlKphuompKSkggAde3alaKiomjt2rUEgJo1a0b79++n48ePk4ODA82dO5eIiCZMmEATJkwgIqLY2FgyNTWlTZs2UUxMDJmYmFBISAgREUVGRpK1tTWtWbOGrly5QvPnzydzc3O6cuUKERF99NFH5OnpSUeOHKGTJ0+Sr68vAaCkpCQiIoqOjqa2bdtq1Ni+fXs6ffo0hYeHk7OzM3388cdERHTp0iUyMzOjjz76iC5fvkwrVqwgCwsL2rJli1SzsbExfffdd5SQkEBTp04lc3Nzys7OrvI1CQ0NJQA0ceJEunLlCl2/fp127NhBLi4utHPnToqPj6dPP/2UHB0dKTMzk8rKysjBwYG++uorSkpKotWrV5NCoaCYmBj6dywBubi40K5duygiIoKeeuopGjNmTI1rr85+Q0NDKS0tjYyMjOjs2bPSc3zuuefok08+ISKiadOmUZcuXej06dMUHR1NAwcOpGeeeabK10YURXJwcKCDBw9Kyx71+iYnJ1d5u3fvXqXtBwcH06xZs6rct4eHBwGggQMHUllZmbR82rRpNH/+/ErrL1++nDw9PTWWvfXWW9ShQwcKDg4mFxcX6tevH8XFxWms8/vvv0vvPX0liiKVlZWRKIoay1UqFeXl5dHdu3cpPj6e/vnnHzpx4gQdOnSIdu/eTZs2baLVq1fTsmXLaOHChfTzzz/TvHnzaP78+bRgwQJasGAB/fLLL7Rw4UJatGgRLV68mJYsWUK//vor/frrr7R06VL67bff6Pfff6eVK1fS5s2bKSQkhI4fP07nz5+nq1evUkpKCuXn55NKpapUt0qlorKysirvY0xXPXZg79u3T1rm7OxMn332mfTziBEj6I033iAizcAmIvriiy/I29ubOnfuTK+88oq0fNy4cTRt2jSNfQ0bNoymTZtGoihSgwYN6K+//pLu2717t0Zg//zzzzR9+nSNGnft2iWt/95771Hv3r2l/3fp0kVjXzNnzqTOnTtLNQcGBkr35eTkEAA6ceJEla+JOrBjY2OlZV27dqUFCxZorNeuXTtasGABZWRkEAD6/fffpfsOHz5MmZmZRFQenPPmzdO4T6lUUnZ2do1qr85+Q0NDiYioT58+9NFHHxERUWZmJhkbG1N0dDTl5+eTiYkJRUdHS9vIysoihUKhsUwtMTGRANDt27elZY/7+j7MwwI7IiKCdu7cSe7u7jRlyhRpeevWrenixYuV1q8qsHv06EFNmzalAwcO0OnTp2nYsGHk7u5Oubm50jonTpwghUKh8aXAEKlDXX27P9xrizqEDf31ZobrsY8BVTwUaW5urnEI1dzcHMXFxVU+7sMPP4SxsTHi4uIwd+5caXlsbCwWLlwIKysr6bZz507Ex8cjPT0daWlpaNOmjbR+hw4dNLZb1YAz9SFcoHxQiXpyidjYWHTq1Elj3aCgIMTGxj7wsUD5Yfljx45p1PjNN99I61V8DWJjYzFjxgyNdaOiohAfHw8HBwe8/fbbeP311+Hp6YnJkyfD1tYW9vb20uOffvpp6f+BgYFQqVSIj4+vUe3V2a/aqFGjsGXLFgDA9u3b4ePjg5YtWyIxMRElJSXo0qWL9Lzc3d0himKVo/PT0tIAAE5OThrLH1QjAI3X7EGvdXUEBgZi4MCB+Pnnn7F06VKUlJQgPT0dqampCAgIqNY2QkJCEBkZiWeffRYdO3bEmjVrUFRUhJ07d0rrODo6QhRFZGRkPFZ9+kZ9mF19q6vTnBQKhbQPxgzRYw86MzLSfEh1+31SU1ORnJyMwsJCREZGIjg4GABQVlaGmTNnYvz48Rrrm5ubS/+nCmeemZiYSP8vKyvDqVOnsH79eo3HVlyn4uPvHwkMlE9CUfFc1fsfq358YGAgIiMjpWUODg6Ijo6utN2ysjLMmzev0pcIdTgtXrwYkyZNwrZt27Bt2zYsXboUO3bsQL9+/QCUDwyqWBtQ/hrXpPbq7Fdt6NCheOuttxATE4PNmzdj5MiR0vMCyk+BsrKy0nhMw4YNK+1X/aF9/3nAD6ux4utbUXVGsKekpODUqVMafeYtWrRASUkJcnNzcfjw4cca/GZqaqrRx2lmZgZvb2/cvn1bWsan+TDG6lO9fdJMmTIFwcHBmDFjBt544w2pJe7n54ekpCQ0a9ZMuv3222/Yu3cvnJyc0LBhQ0REREjbOX/+vPT/iIgItGjRApaWltWqwc/PD+Hh4RrLTp06BT8/v0c+1tzcXKPGB4WIn58fbt26pbHu119/jfDwcNy9exeTJk1Cs2bN8MknnyAiIgK9evXCjh07pMdXDK2zZ8/CxMQEfn5+Naq9OvtVs7W1Rd++fbFhwwYcPHgQo0aNAgA0bdoUSqUSGRkZ0vOysbHBe++9h5SUlErbUYf447Q+K75m1XmtK0pKSsKwYcM0AvXcuXNo0KABnJycHuvUPyJC06ZNNU7Zys/Px5UrV+Dv7y8tS09Ph5GRERwdHav9HBlj7EnVS2Bv2bIFISEhmD9/Pj7++GMUFhbiyy+/BAC89957WL9+PRYsWICEhATMmzcPc+fOha+vLwRBwOTJk/H555/j4MGDOHv2rMaI3sc9//qdd95BZGQkPv74Y8THx2PlypVYtGgRJk2aVGvPddq0aZg3bx5WrVqFhIQEzJw5Exs2bEDz5s3h4OCALVu24L333kNCQgKOHj2KyMhItG3bVnr8559/jiNHjuD06dN49913MWHCBFhZWdWo9urst6JRo0Zh7ty58Pf3l0ZKW1tb4/XXX8fbb7+NsLAwXLp0CePHj8fVq1fh7e1daRuNGzeGk5OTdBSirnXo0AHt27fHK6+8gkuXLmHPnj344IMP8MknnwB4vPeKIAgYMGAAZs2ahbCwMMTExOCll16Cu7s7+vfvL60XHR2Ntm3b8kxXjLF6UWfnYavl5uZiypQpmDlzptT/PXfuXIwZMwajRo1C586dsWrVKsyePRsffPABmjZtinXr1qF79+4AgI8//hj5+fkYOXIkjIyMMGvWLCmkDh06hDlz5lS7Fg8PD+zatQsffPABfvrpJ3h4eGDu3Ll4+eWXa+35jhw5EikpKfjss8+QkpKCgIAA7Ny5U+q73bFjB6ZOnYpWrVrB2toar776Kl577TXp8RMmTMCECROQnZ2N0aNHY968eTWu3cTE5JH7rWjQoEEQRVFqXav99NNPmD59Ol544QWUlpaie/fu2LNnT5V9ioIg4LnnnsPx48c1Qq6uKJVKbN++HZMnT0aXLl1gaWmJd999F++++y6uX78OAI91vvT3338PY2NjjBkzBjk5OXjmmWcqPdfjx49X6lJgjLG6wnOJa5G6nCJUDmFhYXj11VeRkJAgdym1Lj8/H25uboiMjKx07jpjjNUFHi3D6kyPHj3g4uKCAwcOyF1KrVuzZg0GDhzIYc0Yqzcc2KxOLVmyBF999ZXcZdSqkpISLFq0CD/++KPcpTDGDAgfEmeMMcZ0ALewGWOMMR3Agc0YY4zpAA5sxhhjTAdwYDPGGGM6gAObMcYY0wEc2IwxxpgO4MBmjDHGdAAHNmOMMaYDOLAZY4wxHcCBzRhjjOkADmzGGGNMB3BgM8YYYzqAA5sxxhjTARzYjDHGmA7gwGaMMcZ0AAc2Y4wxpgM4sBljjDEd8D84XbBARIb+EQAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "class_order = ['excited', 'inhibited', 'mixed/non-responsive']\n", "class_colors = {\n", "    'excited': '#1b9e77',\n", "    'inhibited': '#d95f02',\n", "    'mixed/non-responsive': '#808080'\n", "}\n", "\n", "# counts across the whole dataset\n", "counts = data['unit_class'].value_counts().reindex(class_order).fillna(0).astype(int)\n", "total = counts.sum()\n", "\n", "# labels with n values relative to total\n", "labels = [f'{cls} (n={counts[cls]}/{total})' for cls in class_order]\n", "colors = [class_colors[c] for c in class_order]\n", "\n", "fig, ax = plt.subplots(figsize=(5, 5))\n", "ax.pie(\n", "    counts.values, labels=labels, colors=colors,\n", "    autopct=lambda p: f'{p:.1f}%' if p > 0 else '', startangle=90, counterclock=False,\n", "    wedgeprops=dict(linewidth=1, edgecolor='white')\n", ")\n", "ax.set_title(f'Response classification (total n={total})')\n", "ax.axis('equal')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 14, "id": "d5c916d1", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1600x400 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "class_order = ['excited', 'inhibited', 'mixed/non-responsive']\n", "class_colors = {'excited': '#1b9e77', 'inhibited': '#d95f02', 'mixed/non-responsive': '#808080'}\n", "subsets = {k: v.copy() for k, v in data.groupby('animal_id')}\n", "\n", "animals = ['b27', 'b28', 'b30']\n", "\n", "fig, axes = plt.subplots(1, 4, figsize=(16, 4))  # 1x4 layout\n", "axes = axes.flatten()\n", "\n", "for ax, animal in zip(axes, animals):\n", "    df_sub = subsets.get(animal, None)\n", "    if df_sub is None or df_sub.empty:\n", "        ax.axis('off')\n", "        ax.set_title(f'{animal} (no data)', pad=2)\n", "        continue\n", "\n", "    counts = df_sub['unit_class'].value_counts().reindex(class_order).fillna(0).astype(int)\n", "    total = int(counts.sum())\n", "\n", "    # labels for legend\n", "    labels = [f'{cls}\\n(n={counts[cls]}/{total})' for cls in class_order]  # split in multiple lines\n", "    colors = [class_colors[c] for c in class_order]\n", "\n", "    wedges, texts, autotexts = ax.pie(\n", "        counts.values,\n", "        colors=colors,\n", "        autopct=lambda p: f'{p:.1f}%' if p > 0 else '',\n", "        startangle=90,\n", "        counterclock=False,\n", "        wedgeprops=dict(linewidth=1, edgecolor='white'),\n", "        textprops={'fontsize': 8}  # smaller %\n", "    )\n", "\n", "    # legend outside\n", "    ax.legend(\n", "        wedges, labels,\n", "        loc='upper center',\n", "        bbox_to_anchor=(0.5, -0.05),\n", "        fontsize=7,  # smaller font\n", "        ncol=1,\n", "        frameon=False\n", "    )\n", "\n", "    ax.set_title(f'{animal} (n={total} units)', pad=2)\n", "    ax.axis('equal')\n", "\n", "fig.tight_layout()\n", "fig.savefig('unit_classification_by_recording.png', dpi=200, bbox_inches='tight')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 15, "id": "f5d09db1", "metadata": {}, "outputs": [], "source": ["from plotting_functions import plot_units_grid_with_shading\n", "\n", "# Build a mapping from unit id -> unit class for border coloring\n", "unit_class_map = dict(zip(data['unit'], data['unit_class']))\n", "\n", "# Create one composite figure per recording (animal_id)\n", "grid_params = dict(\n", "    bin_size=10, t_start=0, t_end=None, n_cols=4,\n", "    figsize_per_subplot=(4.5, 2.0), wspace=0.2, hspace=0.4,\n", "    margins=(0.06, 0.92, 0.08, 0.9), dpi=200\n", ")\n", "for animal, df_sub in data.groupby('animal_id'):\n", "    out_file = f'{animal}_modulation_units_grid.png'\n", "    suptitle = f'{animal}: FR with balloon intervals'\n", "    _ = plot_units_grid_with_shading(\n", "        df_sub,\n", "        bin_size=grid_params['bin_size'], t_start=grid_params['t_start'], t_end=grid_params['t_end'],\n", "        n_cols=grid_params['n_cols'], figsize_per_subplot=grid_params['figsize_per_subplot'],\n", "        wspace=grid_params['wspace'], hspace=grid_params['hspace'], margins=grid_params['margins'],\n", "        suptitle=suptitle, out_path=out_file, dpi=grid_params['dpi'],\n", "        unit_class_map=unit_class_map, unit_col_name='unit'\n", "    )"]}, {"cell_type": "code", "execution_count": 16, "id": "bae03165", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "animal_id", "rawType": "object", "type": "string"}, {"name": "inhibited", "rawType": "int64", "type": "integer"}, {"name": "mixed/non-responsive", "rawType": "int64", "type": "integer"}], "ref": "b7e44129-61af-43be-b7e5-b079ec08bc21", "rows": [["b30", "2", "13"]], "shape": {"columns": 2, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>unit_class</th>\n", "      <th>inhibited</th>\n", "      <th>mixed/non-responsive</th>\n", "    </tr>\n", "    <tr>\n", "      <th>animal_id</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>b30</th>\n", "      <td>2</td>\n", "      <td>13</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["unit_class  inhibited  mixed/non-responsive\n", "animal_id                                  \n", "b30                 2                    13"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# show a table how many excited inhibited and mixed units we have per animal_id, and summarize, with percentages\n", "\n", "data.groupby(['animal_id', 'unit_class']).size().unstack(fill_value=0)\n"]}, {"cell_type": "code", "execution_count": null, "id": "90e7ccb1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 17, "id": "8ba8dc3f", "metadata": {}, "outputs": [], "source": ["from plotting_functions import plot_units_grid_with_shading\n", "\n", "# Build a mapping from unit id -> unit class for border coloring\n", "unit_class_map = dict(zip(data['unit'], data['unit_class']))\n", "\n", "# Create one composite figure per recording (animal_id)\n", "grid_params = dict(\n", "    bin_size=10, t_start=0, t_end=None, n_cols=4,\n", "    figsize_per_subplot=(4.5, 2.0), wspace=0.2, hspace=0.4,\n", "    margins=(0.06, 0.92, 0.08, 0.9), dpi=200\n", ")\n", "for animal, df_sub in data.groupby('animal_id'):\n", "    out_file = f'{animal}_modulation_units_grid.png'\n", "    suptitle = f'{animal}: FR with balloon intervals'\n", "    _ = plot_units_grid_with_shading(\n", "        df_sub,\n", "        bin_size=grid_params['bin_size'], t_start=grid_params['t_start'], t_end=grid_params['t_end'],\n", "        n_cols=grid_params['n_cols'], figsize_per_subplot=grid_params['figsize_per_subplot'],\n", "        wspace=grid_params['wspace'], hspace=grid_params['hspace'], margins=grid_params['margins'],\n", "        suptitle=suptitle, out_path=out_file, dpi=grid_params['dpi'],\n", "        unit_class_map=unit_class_map, unit_col_name='unit'\n", "    )"]}, {"cell_type": "code", "execution_count": 18, "id": "d5fb5543", "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (3474414854.py, line 115)", "output_type": "error", "traceback": ["\u001b[1;36m  Cell \u001b[1;32mIn[18], line 115\u001b[1;36m\u001b[0m\n\u001b[1;33m    \"b27\": data[data[\"animal_id\"] == \"b27\"],\u001b[0m\n\u001b[1;37m    ^\u001b[0m\n\u001b[1;31mSyntaxError\u001b[0m\u001b[1;31m:\u001b[0m invalid syntax\n"]}], "source": ["import numpy as np\n", "import math\n", "from matplotlib import pyplot as plt\n", "\n", "def _get_time_vec(tv):\n", "    if tv is None:\n", "        return lambda i: None\n", "    if isinstance(tv, np.ndarray) and tv.ndim == 1 and tv.dtype != object:\n", "        return lambda i: tv  # shared\n", "    if hasattr(tv, \"iloc\"):  # pandas Series\n", "        return lambda i: tv.iloc[i]\n", "    return lambda i: tv[i]  # list / object array\n", "\n", "def _series_to_list(x):\n", "    return x.tolist() if hasattr(x, \"tolist\") else list(x)\n", "\n", "def plot_recording_waveforms(\n", "    rec_name, rec_data, \n", "    max_cols=8, figsize_per_subplot=(1.5, 1.8),\n", "    save=True, outdir=\".\"\n", "):\n", "    # Accept DataFrame slice or dict\n", "    if hasattr(rec_data, \"__getitem__\") and not isinstance(rec_data, dict):\n", "        if len(rec_data) == 0:\n", "            print(f\"[skip] {rec_name}: no units found.\")\n", "            return\n", "        wf = rec_data[\"waveform_mean\"]\n", "        waveforms = _series_to_list(wf)\n", "        units = _series_to_list(rec_data[\"unit\"]) if \"unit\" in rec_data else None\n", "        tv_col = rec_data[\"waveform_time_vector_ms\"] if \"waveform_time_vector_ms\" in rec_data else None\n", "    else:\n", "        if rec_data is None:\n", "            print(f\"[skip] {rec_name}: data is None.\")\n", "            return\n", "        wf = rec_data.get(\"waveform_mean\", [])\n", "        if isinstance(wf, np.ndarray) and wf.ndim == 2:\n", "            waveforms = [wf[i, :] for i in range(wf.shape[0])]\n", "        else:\n", "            waveforms = list(wf)\n", "        units = rec_data.get(\"unit\", None)\n", "        tv_col = rec_data.get(\"waveform_time_vector_ms\", None)\n", "\n", "    n_units = len(waveforms)\n", "    if n_units == 0:\n", "        print(f\"[skip] {rec_name}: no units found.\")\n", "        return\n", "\n", "    tv_get = _get_time_vec(tv_col)\n", "\n", "    # Grid\n", "    ncols = min(max_cols, max(1, int(math.ceil(math.sqrt(n_units)))))\n", "    nrows = int(math.ceil(n_units / ncols))\n", "\n", "    fig_w = figsize_per_subplot[0] * ncols\n", "    fig_h = figsize_per_subplot[1] * nrows\n", "    fig, axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(fig_w, fig_h))\n", "\n", "    if not isinstance(axes, np.ndarray):\n", "        axes = np.array([axes])\n", "    axes = axes.reshape(nrows, ncols)\n", "\n", "    for i, ax in enumerate(axes.flat):\n", "        if i < n_units:\n", "            y = np.asarray(waveforms[i]).ravel()\n", "            x = tv_get(i)\n", "            if x is None:\n", "                x = np.arange(len(y))\n", "            ax.plot(x, y, color=\"black\")\n", "            if units is not None and i < len(units):\n", "                ax.set_title(f\"Unit {units[i]}\", fontsize=7)\n", "            ax.set_xticks([]); ax.set_yticks([])\n", "            ax.axis(\"off\")\n", "        else:\n", "            ax.axis(\"off\")\n", "\n", "    # ---- Add 1 ms scalebar INSIDE the last subplot without changing limits ----\n", "    last_ax = axes.flat[min(n_units, len(axes.flat)) - 1]\n", "    # Save current limits (so the scalebar doesn't shrink the waveform)\n", "    xlim = last_ax.get_xlim()\n", "    ylim = last_ax.get_ylim()\n", "\n", "    xspan = xlim[1] - xlim[0]\n", "    yspan = ylim[1] - ylim[0]\n", "\n", "    # Desired bar length (1 ms in data units)\n", "    bar_len = 1.0  # ms\n", "    # If the axis window is <1 ms, fall back to 50% of the span\n", "    if bar_len > xspan:\n", "        bar_len = 0.5 * xspan\n", "\n", "    # Place near bottom-right with a small margin\n", "    x_margin = 0.05 * xspan\n", "    y_margin = 0.08 * yspan\n", "    x_end = xlim[1] - x_margin\n", "    x_start = x_end - bar_len\n", "    y_bar = ylim[0] + y_margin\n", "\n", "    # Draw the bar + label, then restore limits so autoscale doesn't change\n", "    last_ax.plot([x_start, x_end], [y_bar, y_bar], color=\"black\", lw=2, solid_capstyle=\"butt\", clip_on=False, zorder=5)\n", "    last_ax.text((x_start + x_end) / 2, y_bar, \"1 ms\", ha=\"center\", va=\"bottom\", fontsize=7)\n", "\n", "    last_ax.set_xlim(xlim)\n", "    last_ax.set_ylim(ylim)\n", "    # --------------------------------------------------------------------------\n", "\n", "    fig.suptitle(f\"Recording: {rec_name} — mean waveforms ({n_units} units)\", fontsize=10)\n", "    fig.tight_layout(rect=[0, 0, 1, 0.94])\n", "\n", "    if save:\n", "        plt.savefig(f\"{outdir}/waveforms_{rec_name}.png\", dpi=200)\n", "    plt.show()\n", "\n", "# Build dictionary of the recordings of interest\n", "recordings = {a\n", "    \"b27\": data[data[\"animal_id\"] == \"b27\"],\n", "    \"b28\": data[data[\"animal_id\"] == \"b28\"],\n", "    \"b30\": data[data[\"animal_id\"] == \"b30\"],\n", "}\n", "\n", "# Plot all of them\n", "plot_all_recordings(recordings)"]}, {"cell_type": "code", "execution_count": null, "id": "cd9487d3", "metadata": {}, "outputs": [], "source": ["# Build dictionary of the recordings of interest\n", "recordings = {\n", "    \"b24\": data[data[\"animal_id\"] == \"b24\"],\n", "    \"b25\": data[data[\"animal_id\"] == \"b25\"],\n", "    \"b27\": data[data[\"animal_id\"] == \"b27\"],\n", "    \"b28\": data[data[\"animal_id\"] == \"b28\"],\n", "    \"b30\": data[data[\"animal_id\"] == \"b30\"],\n", "}\n", "\n", "# Plot all of them\n", "plot_all_recordings(recordings)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}