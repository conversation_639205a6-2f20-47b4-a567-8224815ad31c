# Standard library imports
from pathlib import Path
from typing import List, Dict, Optional

# Third-party imports
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.colors import TwoSlopeNorm
from tabulate import tabulate

# Local module imports
from npx_utils import extract_unit_data
from modulation_functions import (
    modulation_index_single_trial,
    compute_trialwise_mi,
    compute_trialwise_mi_null,
    modulation_flag,
    compute_trialwise_modulation_flags
)
from plotting_functions import plot_units_grid_with_shading

# Configuration constants
MINIMUM_FIRING_RATE_HZ = 0.05
BALLOON_OFFSET_DURATION_SEC = 200.0
NULL_DISTRIBUTION_ITERATIONS = 10000
MODULATION_WINDOW_LENGTH_SAMPLES = 40
RANDOM_SEED = 42

# Time limits (seconds) for filtering balloon events per animal
# -1 indicates no time limit
TIME_LIMITS_BY_ANIMAL = {
    "b27": -1, 
    "b28": -1, 
    "b30": -1
}

# Dataset paths for Kilosort output folders
DATASET_PATHS = [
    # Uncomment additional datasets as needed
    Path(r"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b27\\b27_p1_r1_g0_p\\catgt"),
    Path(r"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b28\\b28_p2_r1_g0\\catgt"),
    Path(r"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b30\\b30_p1_r1_g0\\catgt")
]

# Mapping from recording directory names to animal IDs
RECORDING_TO_ANIMAL_MAPPING = {
    "b27_p1_r1_g0_p": "b27",
    "b28_p2_r1_g0": "b28",
    "b30_p1_r1_g0": "b30"
}

# Extract unit data from Kilosort datasets and filter by firing rate
for dataset_path in DATASET_PATHS:
    recording_id = dataset_path.parent.name
    animal_id = RECORDING_TO_ANIMAL_MAPPING.get(recording_id, "")
    
    print(f"[Unit Extraction] Processing: {dataset_path}")
    print(f"  Recording ID: {recording_id}, Animal ID: {animal_id}")
    
    try:
        # Extract unit data with good and MUA quality using imported function
        unit_dataframe = extract_unit_data(
            datapath=str(dataset_path), 
            animal_id=animal_id, 
            quality=['good', 'mua']
        )
        
        # Filter by minimum firing rate
        units_before_filter = len(unit_dataframe)
        unit_dataframe = unit_dataframe[
            unit_dataframe['mean_firing_rate'] > MINIMUM_FIRING_RATE_HZ
        ]
        units_after_filter = len(unit_dataframe)
        
        # Save filtered data
        output_file = dataset_path / 'unit_summary.pkl'
        unit_dataframe.to_pickle(output_file)
        
        print(f"  ✔ Filtered {units_before_filter} → {units_after_filter} units")
        print(f"  ✔ Saved: {output_file}")
        
    except Exception as error:
        print(f"  ❌ Failed to process {dataset_path}: {error}")

# Load balloon event timing data and integrate with unit summaries
for dataset_path in DATASET_PATHS:
    recording_id = dataset_path.parent.name
    animal_id = RECORDING_TO_ANIMAL_MAPPING.get(recording_id, "")
    base_path = dataset_path.parent
    
    # Define file paths
    balloon_on_file = base_path / 'bal_on_sec.txt'
    balloon_off_file = base_path / 'bal_off_sec.txt'
    unit_summary_file = dataset_path / 'unit_summary.pkl'
    
    status_prefix = f"[Balloon Integration] {recording_id} ({animal_id})"
    
    # Validation checks
    if not animal_id:
        print(f"{status_prefix} ❌ Animal ID not found. Skipping.")
        continue
        
    if not balloon_on_file.exists() or not balloon_off_file.exists():
        print(f"{status_prefix} ⚠ Missing balloon timing files")
        continue
        
    if not unit_summary_file.exists():
        print(f"{status_prefix} ⚠ Missing unit summary file - run extraction first")
        continue
    
    try:
        # Load balloon timing data
        balloon_on_times = np.atleast_2d(np.loadtxt(balloon_on_file, delimiter='\t'))
        balloon_off_times = np.atleast_2d(np.loadtxt(balloon_off_file, delimiter='\t'))
        events_before_filter = len(balloon_on_times)
        
        # Apply time limits if specified
        time_limit = float(TIME_LIMITS_BY_ANIMAL.get(animal_id, -1))
        if time_limit > 0:
            time_mask = (
                (balloon_on_times[:, 0] < time_limit) & 
                (balloon_on_times[:, 1] < time_limit) &
                (balloon_off_times[:, 0] < time_limit) & 
                (balloon_off_times[:, 1] < time_limit)
            )
            balloon_on_times = balloon_on_times[time_mask]
            balloon_off_times = balloon_off_times[time_mask]
        
        events_after_filter = len(balloon_on_times)
        
        # Load unit data and add balloon timing columns
        unit_dataframe = pd.read_pickle(unit_summary_file)
        unit_dataframe['balloon_on_sec'] = [balloon_on_times.tolist()] * len(unit_dataframe)
        unit_dataframe['balloon_off_sec'] = [balloon_off_times.tolist()] * len(unit_dataframe)
        
        # Save updated data
        unit_dataframe.to_pickle(unit_summary_file)
        
        print(f"{status_prefix} ✔ Integrated {events_before_filter}→{events_after_filter} events (time_limit={time_limit})")
        
    except Exception as error:
        print(f"{status_prefix} ❌ Integration failed: {error}")

# Generate summary table of unit counts and balloon events for each dataset
summary_data = []

for dataset_path in DATASET_PATHS:
    recording_id = dataset_path.parent.name
    animal_id = RECORDING_TO_ANIMAL_MAPPING.get(recording_id, "")
    unit_summary_file = dataset_path / "unit_summary.pkl"
    
    if not unit_summary_file.exists():
        continue
    
    try:
        unit_dataframe = pd.read_pickle(unit_summary_file)
        
        # Count units by quality
        good_unit_count = (
            int((unit_dataframe.get("quality") == "good").sum()) 
            if "quality" in unit_dataframe else None
        )
        mua_unit_count = (
            int((unit_dataframe.get("quality") == "mua").sum()) 
            if "quality" in unit_dataframe else None
        )
        
        # Count balloon events
        balloon_on_count = (
            len(unit_dataframe.get("balloon_on_sec", [[]])[0]) 
            if "balloon_on_sec" in unit_dataframe else 0
        )
        balloon_off_count = (
            len(unit_dataframe.get("balloon_off_sec", [[]])[0]) 
            if "balloon_off_sec" in unit_dataframe else 0
        )
        
        summary_data.append({
            "recording_id": recording_id,
            "animal_id": animal_id,
            "units": len(unit_dataframe),
            "good_units": good_unit_count,
            "mua_units": mua_unit_count,
            "balloon_on": balloon_on_count,
            "balloon_off": balloon_off_count,
        })
        
    except Exception as error:
        print(f"  ⚠ Summary generation failed for {recording_id}: {error}")

# Create and display summary DataFrame
dataset_summary = pd.DataFrame(summary_data)

if not dataset_summary.empty:
    print("\n=== Dataset Summary ===\n")
    print(tabulate(dataset_summary, headers="keys", tablefmt="github", showindex=True))
else:
    print("No summary data available - check dataset paths and processing steps.")

# Define paths to unit summary files
unit_summary_paths = {
    # Uncomment additional datasets as needed
    # 'b27': Path(r"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b27\\b27_p1_r1_g0_p\\catgt\\unit_summary.pkl"),
    # 'b28': Path(r"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b28\\b28_p2_r1_g0\\catgt\\unit_summary.pkl"),
    'b30': Path(r"Z:\\users\\izouridis\\projects\\bal_npx\\data\\b30\\b30_p1_r1_g0\\catgt\\unit_summary.pkl")
}

# Load data from existing files
dataframes = []
available_datasets = []

for dataset_name, file_path in unit_summary_paths.items():
    if not file_path.exists():
        print(f"⚠ Missing file: {file_path}")
        continue
        
    unit_dataframe = pd.read_pickle(file_path).copy()
    dataframes.append(unit_dataframe)
    available_datasets.append(dataset_name)

# Combine all datasets
if dataframes:
    neural_data = pd.concat(dataframes, ignore_index=True)
else:
    neural_data = pd.DataFrame()

print(f"Loaded {len(neural_data)} units from datasets: {available_datasets}")

if not neural_data.empty:
    # Create unique unit identifiers
    neural_data['unit'] = (
        neural_data['animal_id'] + '_' + neural_data['unit'].astype(str)
    )
    
    # Remove unnecessary columns to reduce memory usage
    columns_to_remove = ['spike_times_samples', 'peak_channel', 'depth']
    existing_columns_to_remove = [
        col for col in columns_to_remove if col in neural_data.columns
    ]
    neural_data.drop(columns=existing_columns_to_remove, inplace=True)
    
    # Adjust balloon OFF timing by subtracting offset duration
    neural_data["balloon_off_sec"] = neural_data["balloon_off_sec"].apply(
        lambda intervals: [
            [max(end_time - BALLOON_OFFSET_DURATION_SEC, 0.0), max(end_time, 0.0)] 
            if np.isfinite(end_time) else [start_time, end_time] 
            for start_time, end_time in intervals
        ]
    )

# Perform modulation analysis if data is available
if not neural_data.empty:
    print("Calculating modulation indices using imported functions...")
    
    # Calculate observed modulation indices using imported function
    neural_data['modulation_index'] = neural_data.apply(compute_trialwise_mi, axis=1)
    
    print("Generating null distributions for statistical testing...")
    
    # Generate null distributions for statistical significance testing
    neural_data['modulation_index_null'] = neural_data.apply(
        lambda row: compute_trialwise_mi_null(
            row,
            window_len=MODULATION_WINDOW_LENGTH_SAMPLES,
            n_iter=NULL_DISTRIBUTION_ITERATIONS,
            mi_formula='difference_over_sum',
            seed=RANDOM_SEED
        ),
        axis=1
    )
    
    print("Determining modulation significance flags...")
    
    # Determine statistical significance flags for each trial using imported function
    # -1: significantly suppressed, 0: not modulated, 1: significantly enhanced
    neural_data['modulated_trial'] = neural_data.apply(
        lambda row: compute_trialwise_modulation_flags(
            row, 
            lower_quantile=0.1, 
            upper_quantile=0.9
        ),
        axis=1
    )
    
    print("Modulation analysis complete.")
else:
    print("No data available for modulation analysis.")

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import TwoSlopeNorm

def plot_mi_heatmap(
    data,
    *,
    mi_col: str = "modulation_index",
    unit_col: str = "unit",
    vmax: float | None = None
):
    """
    Heatmap of modulation index (trials × units), diverging colormap:
      blue < 0 < red, with 0 in white. NaNs shown in light gray.

    X-axis: Unit id (unit names from `unit_col`)
    Y-axis: trial1, trial2, ...
    """
    # Collect MI lists per unit
    mi_lists = data[mi_col].tolist()
    n_units = len(mi_lists)
    max_trials = max((len(m) for m in mi_lists), default=0)

    # Build matrix (units x trials), then transpose to (trials x units)
    M = np.full((n_units, max_trials), np.nan, dtype=float)
    for i, mis in enumerate(mi_lists):
        M[i, :len(mis)] = np.asarray(mis, dtype=float)
    M = M.T  # now shape = (max_trials, n_units): rows=trials, cols=units

    # Symmetric normalization around 0
    if vmax is None:
        finite = M[np.isfinite(M)]
        abs_max = np.max(np.abs(finite)) if finite.size else 1.0
    else:
        abs_max = float(vmax)
    norm = TwoSlopeNorm(vmin=-abs_max, vcenter=0.0, vmax=+abs_max)

    # Diverging colormap with white center; gray for NaN
    cmap = plt.get_cmap("bwr").copy()
    cmap.set_bad("0.85", alpha=1.0)  # NaNs as light gray

    # Figure size: wide for many units, tall for many trials
    fig_w = max(6, 0.35 * n_units)
    fig_h = max(4, 0.28 * max_trials)

    fig, ax = plt.subplots(figsize=(fig_w, fig_h))
    im = ax.imshow(M, aspect="auto", interpolation="nearest", cmap=cmap, norm=norm)

    # Colorbar
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label("Modulation index")

    # Tick labels
    # X: units
    units = data[unit_col].astype(str).tolist() if unit_col in data.columns else [str(i) for i in range(n_units)]
    ax.set_xticks(np.arange(n_units))
    ax.set_xticklabels(units, rotation=90, ha="center", va="top")

    # Y: trials as trial1, trial2, ...
    ax.set_yticks(np.arange(max_trials))
    ax.set_yticklabels([f"trial{i+1}" for i in range(max_trials)])
    ax.invert_yaxis()  # put trial1 at the top

    # Axis labels & title
    ax.set_xlabel("Unit id")
    ax.set_ylabel("(Trial #)")

    fig.tight_layout()
    plt.show()

# usage:
plot_mi_heatmap(neural_data)


def classify_unit(modulated_trial):
    n = len(modulated_trial)
    n_pos = sum(x == 1 for x in modulated_trial)
    n_neg = sum(x == -1 for x in modulated_trial)

    if n_pos >= n / 2 and n_neg == 0:
        return "excited"
    elif n_neg >= n / 2 and n_pos == 0:
        return "inhibited"
    else:
        return "mixed/non-responsive"

neural_data['unit_class'] = neural_data['modulated_trial'].apply(classify_unit)


import matplotlib.pyplot as plt

class_order = ['excited', 'inhibited', 'mixed/non-responsive']
class_colors = {
    'excited': '#1b9e77',
    'inhibited': '#d95f02',
    'mixed/non-responsive': '#808080'
}

# counts across the whole dataset
counts = neural_data['unit_class'].value_counts().reindex(class_order).fillna(0).astype(int)
total = counts.sum()

# labels with n values relative to total
labels = [f'{cls} (n={counts[cls]}/{total})' for cls in class_order]
colors = [class_colors[c] for c in class_order]

fig, ax = plt.subplots(figsize=(5, 5))
ax.pie(
    counts.values, labels=labels, colors=colors,
    autopct=lambda p: f'{p:.1f}%' if p > 0 else '', startangle=90, counterclock=False,
    wedgeprops=dict(linewidth=1, edgecolor='white')
)
ax.set_title(f'Response classification (total n={total})')
ax.axis('equal')
plt.show()


import matplotlib.pyplot as plt

class_order = ['excited', 'inhibited', 'mixed/non-responsive']
class_colors = {'excited': '#1b9e77', 'inhibited': '#d95f02', 'mixed/non-responsive': '#808080'}
subsets = {k: v.copy() for k, v in neural_data.groupby('animal_id')}

animals = ['b27', 'b28', 'b30']

fig, axes = plt.subplots(1, 4, figsize=(16, 4))  # 1x4 layout
axes = axes.flatten()

for ax, animal in zip(axes, animals):
    df_sub = subsets.get(animal, None)
    if df_sub is None or df_sub.empty:
        ax.axis('off')
        ax.set_title(f'{animal} (no data)', pad=2)
        continue

    counts = df_sub['unit_class'].value_counts().reindex(class_order).fillna(0).astype(int)
    total = int(counts.sum())

    # labels for legend
    labels = [f'{cls}\n(n={counts[cls]}/{total})' for cls in class_order]  # split in multiple lines
    colors = [class_colors[c] for c in class_order]

    wedges, texts, autotexts = ax.pie(
        counts.values,
        colors=colors,
        autopct=lambda p: f'{p:.1f}%' if p > 0 else '',
        startangle=90,
        counterclock=False,
        wedgeprops=dict(linewidth=1, edgecolor='white'),
        textprops={'fontsize': 8}  # smaller %
    )

    # legend outside
    ax.legend(
        wedges, labels,
        loc='upper center',
        bbox_to_anchor=(0.5, -0.05),
        fontsize=7,  # smaller font
        ncol=1,
        frameon=False
    )

    ax.set_title(f'{animal} (n={total} units)', pad=2)
    ax.axis('equal')

fig.tight_layout()
fig.savefig('unit_classification_by_recording.png', dpi=200, bbox_inches='tight')
plt.show()


from plotting_functions import plot_units_grid_with_shading

# Build a mapping from unit id -> unit class for border coloring
unit_class_map = dict(zip(data['unit'], data['unit_class']))

# Create one composite figure per recording (animal_id)
grid_params = dict(
    bin_size=10, t_start=0, t_end=None, n_cols=4,
    figsize_per_subplot=(4.5, 2.0), wspace=0.2, hspace=0.4,
    margins=(0.06, 0.92, 0.08, 0.9), dpi=200
)
for animal, df_sub in data.groupby('animal_id'):
    out_file = f'{animal}_modulation_units_grid.png'
    suptitle = f'{animal}: FR with balloon intervals'
    _ = plot_units_grid_with_shading(
        df_sub,
        bin_size=grid_params['bin_size'], t_start=grid_params['t_start'], t_end=grid_params['t_end'],
        n_cols=grid_params['n_cols'], figsize_per_subplot=grid_params['figsize_per_subplot'],
        wspace=grid_params['wspace'], hspace=grid_params['hspace'], margins=grid_params['margins'],
        suptitle=suptitle, out_path=out_file, dpi=grid_params['dpi'],
        unit_class_map=unit_class_map, unit_col_name='unit'
    )

# show a table how many excited inhibited and mixed units we have per animal_id, and summarize, with percentages

data.groupby(['animal_id', 'unit_class']).size().unstack(fill_value=0)




from plotting_functions import plot_units_grid_with_shading

# Build a mapping from unit id -> unit class for border coloring
unit_class_map = dict(zip(data['unit'], data['unit_class']))

# Create one composite figure per recording (animal_id)
grid_params = dict(
    bin_size=10, t_start=0, t_end=None, n_cols=4,
    figsize_per_subplot=(4.5, 2.0), wspace=0.2, hspace=0.4,
    margins=(0.06, 0.92, 0.08, 0.9), dpi=200
)
for animal, df_sub in data.groupby('animal_id'):
    out_file = f'{animal}_modulation_units_grid.png'
    suptitle = f'{animal}: FR with balloon intervals'
    _ = plot_units_grid_with_shading(
        df_sub,
        bin_size=grid_params['bin_size'], t_start=grid_params['t_start'], t_end=grid_params['t_end'],
        n_cols=grid_params['n_cols'], figsize_per_subplot=grid_params['figsize_per_subplot'],
        wspace=grid_params['wspace'], hspace=grid_params['hspace'], margins=grid_params['margins'],
        suptitle=suptitle, out_path=out_file, dpi=grid_params['dpi'],
        unit_class_map=unit_class_map, unit_col_name='unit'
    )

import numpy as np
import math
from matplotlib import pyplot as plt

def _get_time_vec(tv):
    if tv is None:
        return lambda i: None
    if isinstance(tv, np.ndarray) and tv.ndim == 1 and tv.dtype != object:
        return lambda i: tv  # shared
    if hasattr(tv, "iloc"):  # pandas Series
        return lambda i: tv.iloc[i]
    return lambda i: tv[i]  # list / object array

def _series_to_list(x):
    return x.tolist() if hasattr(x, "tolist") else list(x)

def plot_recording_waveforms(
    rec_name, rec_data, 
    max_cols=8, figsize_per_subplot=(1.5, 1.8),
    save=True, outdir="."
):
    # Accept DataFrame slice or dict
    if hasattr(rec_data, "__getitem__") and not isinstance(rec_data, dict):
        if len(rec_data) == 0:
            print(f"[skip] {rec_name}: no units found.")
            return
        wf = rec_data["waveform_mean"]
        waveforms = _series_to_list(wf)
        units = _series_to_list(rec_data["unit"]) if "unit" in rec_data else None
        tv_col = rec_data["waveform_time_vector_ms"] if "waveform_time_vector_ms" in rec_data else None
    else:
        if rec_data is None:
            print(f"[skip] {rec_name}: data is None.")
            return
        wf = rec_data.get("waveform_mean", [])
        if isinstance(wf, np.ndarray) and wf.ndim == 2:
            waveforms = [wf[i, :] for i in range(wf.shape[0])]
        else:
            waveforms = list(wf)
        units = rec_data.get("unit", None)
        tv_col = rec_data.get("waveform_time_vector_ms", None)

    n_units = len(waveforms)
    if n_units == 0:
        print(f"[skip] {rec_name}: no units found.")
        return

    tv_get = _get_time_vec(tv_col)

    # Grid
    ncols = min(max_cols, max(1, int(math.ceil(math.sqrt(n_units)))))
    nrows = int(math.ceil(n_units / ncols))

    fig_w = figsize_per_subplot[0] * ncols
    fig_h = figsize_per_subplot[1] * nrows
    fig, axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(fig_w, fig_h))

    if not isinstance(axes, np.ndarray):
        axes = np.array([axes])
    axes = axes.reshape(nrows, ncols)

    for i, ax in enumerate(axes.flat):
        if i < n_units:
            y = np.asarray(waveforms[i]).ravel()
            x = tv_get(i)
            if x is None:
                x = np.arange(len(y))
            ax.plot(x, y, color="black")
            if units is not None and i < len(units):
                ax.set_title(f"Unit {units[i]}", fontsize=7)
            ax.set_xticks([]); ax.set_yticks([])
            ax.axis("off")
        else:
            ax.axis("off")

    # ---- Add 1 ms scalebar INSIDE the last subplot without changing limits ----
    last_ax = axes.flat[min(n_units, len(axes.flat)) - 1]
    # Save current limits (so the scalebar doesn't shrink the waveform)
    xlim = last_ax.get_xlim()
    ylim = last_ax.get_ylim()

    xspan = xlim[1] - xlim[0]
    yspan = ylim[1] - ylim[0]

    # Desired bar length (1 ms in data units)
    bar_len = 1.0  # ms
    # If the axis window is <1 ms, fall back to 50% of the span
    if bar_len > xspan:
        bar_len = 0.5 * xspan

    # Place near bottom-right with a small margin
    x_margin = 0.05 * xspan
    y_margin = 0.08 * yspan
    x_end = xlim[1] - x_margin
    x_start = x_end - bar_len
    y_bar = ylim[0] + y_margin

    # Draw the bar + label, then restore limits so autoscale doesn't change
    last_ax.plot([x_start, x_end], [y_bar, y_bar], color="black", lw=2, solid_capstyle="butt", clip_on=False, zorder=5)
    last_ax.text((x_start + x_end) / 2, y_bar, "1 ms", ha="center", va="bottom", fontsize=7)

    last_ax.set_xlim(xlim)
    last_ax.set_ylim(ylim)
    # --------------------------------------------------------------------------

    fig.suptitle(f"Recording: {rec_name} — mean waveforms ({n_units} units)", fontsize=10)
    fig.tight_layout(rect=[0, 0, 1, 0.94])

    if save:
        plt.savefig(f"{outdir}/waveforms_{rec_name}.png", dpi=200)
    plt.show()

# Build dictionary of the recordings of interest
recordings = {a
    "b27": data[data["animal_id"] == "b27"],
    "b28": data[data["animal_id"] == "b28"],
    "b30": data[data["animal_id"] == "b30"],
}

# Plot all of them
plot_all_recordings(recordings)

# Build dictionary of the recordings of interest
recordings = {
    "b24": data[data["animal_id"] == "b24"],
    "b25": data[data["animal_id"] == "b25"],
    "b27": data[data["animal_id"] == "b27"],
    "b28": data[data["animal_id"] == "b28"],
    "b30": data[data["animal_id"] == "b30"],
}

# Plot all of them
plot_all_recordings(recordings)
